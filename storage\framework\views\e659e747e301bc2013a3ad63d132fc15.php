<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Welcome Back, <?php echo e(Auth::user()->name); ?>

                </h1>
                <p class="text-gray-300 text-lg">Manage your store and track your success</p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('vendor.products.create')); ?>"
                   class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center space-x-2">
                    <i class="fa-solid fa-plus transition-transform duration-300 group-hover:rotate-90"></i>
                    <span>Add Product</span>
                </a>
                <a href="<?php echo e(route('vendor.orders.index')); ?>"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-shopping-cart transition-transform duration-300 group-hover:bounce"></i>
                    <span>View Orders</span>
                </a>
            </div>
        </div>
    </div>

    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-money-bill-wave text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full <?php echo e($salesGrowth >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?> transition-all duration-300">
                        <?php echo e($salesGrowth >= 0 ? '↗' : '↘'); ?> <?php echo e(abs($salesGrowth)); ?>%
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Sales</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">
                    <?php echo '₦' . number_format($totalSales, 2); ?>
                </p>
                <p class="text-xs text-gray-400">vs last month</p>
            </div>
        </div>
        
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-shopping-cart text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full <?php echo e($orderGrowth >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?> transition-all duration-300">
                        <?php echo e($orderGrowth >= 0 ? '↗' : '↘'); ?> <?php echo e(abs($orderGrowth)); ?>%
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Orders</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    <?php echo e(number_format($totalOrders)); ?>

                </p>
                <p class="text-xs text-gray-400">vs last month</p>
            </div>
        </div>
        
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-box text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-blue-100 text-blue-800 transition-all duration-300">
                        +<?php echo e($newProductsThisMonth); ?> new
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Products</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">
                    <?php echo e(number_format($totalProducts)); ?>

                </p>
                <p class="text-xs text-gray-400">this month</p>
            </div>
        </div>
        
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-crown text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <!--[if BLOCK]><![endif]--><?php if($daysRemaining > 0): ?>
                        <span class="text-xs font-semibold px-3 py-1 rounded-full bg-green-100 text-green-800">
                            <?php echo e($daysRemaining); ?>d left
                        </span>
                    <?php else: ?>
                        <span class="text-xs font-semibold px-3 py-1 rounded-full bg-red-100 text-red-800">
                            Expired
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Subscription</p>
                <p class="text-2xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">
                    <?php echo e($subscriptionName); ?>

                </p>
                <!--[if BLOCK]><![endif]--><?php if($daysRemaining <= 0): ?>
                    <a href="<?php echo e(route('vendor.subscription.index')); ?>" class="text-xs text-red-600 hover:text-red-800 font-medium transition-colors duration-300">
                        Renew now →
                    </a>
                <?php else: ?>
                    <p class="text-xs text-gray-400"><?php echo e($daysRemaining); ?> days remaining</p>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>
            </div>

    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <div class="lg:col-span-2 space-y-8">
            
            <div class="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900">Sales Overview</h3>
                        <p class="text-gray-500 mt-1">Track your revenue performance</p>
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-300">
                            7D
                        </button>
                        <button class="px-4 py-2 text-sm font-medium text-white bg-black rounded-lg hover:bg-gray-800 transition-colors duration-300">
                            30D
                        </button>
                        <button class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-300">
                            90D
                        </button>
                    </div>
                </div>
                <div wire:ignore class="h-80">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>

            
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
                <div class="p-8 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Recent Orders</h3>
                            <p class="text-gray-500 mt-1">Latest customer orders</p>
                        </div>
                        <a href="<?php echo e(route('vendor.orders.index')); ?>"
                           class="group inline-flex items-center px-4 py-2 bg-black text-white rounded-xl font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                            <span>View All</span>
                            <i class="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                        </a>
                    </div>
                </div>
                <div class="overflow-hidden">
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="p-6 border-b border-gray-50 hover:bg-gray-50 transition-all duration-300 <?php echo e($index === 0 ? 'bg-gradient-to-r from-blue-50 to-transparent' : ''); ?>">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white font-bold">
                                        #<?php echo e($order->id); ?>

                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900"><?php echo e($order->user->name ?? 'Guest Customer'); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo e($order->created_at->format('M d, Y \a\t g:i A')); ?></p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="text-right">
                                        <p class="font-bold text-lg text-gray-900"><?php echo '₦' . number_format($order->items->sum(fn($item) => $item->price * $item->quantity), 2); ?></p>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <?php echo e($order->status === 'completed' ? 'bg-green-100 text-green-800' : ($order->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')); ?>">
                                            <?php echo e(ucfirst($order->status)); ?>

                                        </span>
                                    </div>
                                    <a href="<?php echo e(route('vendor.orders.show', $order)); ?>"
                                       class="p-2 text-gray-400 hover:text-black hover:bg-gray-100 rounded-lg transition-all duration-300">
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="p-12 text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-shopping-cart text-gray-400 text-xl"></i>
                            </div>
                            <p class="text-gray-500 font-medium">No orders yet</p>
                            <p class="text-gray-400 text-sm mt-1">Orders will appear here once customers start purchasing</p>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
        </div>

        
        <div class="lg:col-span-1 space-y-8">
            
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">Top Products</h3>
                            <p class="text-gray-500 text-sm mt-1">Best performing items</p>
                        </div>
                        <a href="<?php echo e(route('vendor.products.index')); ?>"
                           class="text-black hover:text-gray-600 transition-colors duration-300 font-medium text-sm">
                            View All →
                        </a>
                    </div>
                </div>
                <div class="p-6 space-y-4">
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $productPerformance; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="group flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-all duration-300">
                            <div class="relative">
                                <img src="<?php echo e($product->image_url ?? asset('storage/product-placeholder.png')); ?>"
                                     alt="<?php echo e($product->name); ?>"
                                     class="w-12 h-12 rounded-lg object-cover group-hover:scale-105 transition-transform duration-300">
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                    <?php echo e($index + 1); ?>

                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-300">
                                    <?php echo e($product->name); ?>

                                </p>
                                <p class="text-sm text-gray-500"><?php echo '₦' . number_format($product->price, 2); ?></p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-900 text-lg"><?php echo e($product->total_sold); ?></p>
                                <p class="text-xs text-gray-500">sold</p>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-8">
                            <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-box text-gray-400"></i>
                            </div>
                            <p class="text-gray-500 font-medium">No products yet</p>
                            <p class="text-gray-400 text-sm">Add products to see performance</p>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

            
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
                <div class="p-6 border-b border-gray-100">
                    <h3 class="text-xl font-bold text-gray-900">Quick Actions</h3>
                    <p class="text-gray-500 text-sm mt-1">Manage your store</p>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo e(route('vendor.orders.index')); ?>"
                       class="group w-full flex items-center px-4 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl text-blue-700 hover:from-blue-100 hover:to-indigo-100 hover:border-blue-300 transition-all duration-300 hover:scale-105">
                        <div class="p-2 bg-blue-500 rounded-lg mr-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-shopping-cart text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-semibold">Manage Orders</p>
                            <p class="text-xs text-blue-600">View and process orders</p>
                        </div>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>

                    <a href="<?php echo e(route('vendor.settings.index')); ?>"
                       class="group w-full flex items-center px-4 py-4 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl text-purple-700 hover:from-purple-100 hover:to-pink-100 hover:border-purple-300 transition-all duration-300 hover:scale-105">
                        <div class="p-2 bg-purple-500 rounded-lg mr-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-store text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-semibold">Shop Settings</p>
                            <p class="text-xs text-purple-600">Configure your store</p>
                        </div>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>

                    <a href="<?php echo e(route('vendor.subscription.index')); ?>"
                       class="group w-full flex items-center px-4 py-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl text-yellow-700 hover:from-yellow-100 hover:to-orange-100 hover:border-yellow-300 transition-all duration-300 hover:scale-105">
                        <div class="p-2 bg-yellow-500 rounded-lg mr-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-crown text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-semibold">Subscription</p>
                            <p class="text-xs text-yellow-600">Manage your plan</p>
                        </div>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    
    <div style="display: none;">
        <?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('livewire:load', function () {
        const salesChartCtx = document.getElementById('salesChart').getContext('2d');

        // Create gradient for modern look
        const gradient = salesChartCtx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(0, 0, 0, 0.2)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.02)');

        // Fetch chart data from a Livewire method
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('getAnalyticsData').then(response => {
            const salesData = response.sales_over_time;
            const labels = salesData.map(d => d.date);
            const data = salesData.map(d => d.total);

            new Chart(salesChartCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Sales Revenue',
                        data: data,
                        borderColor: '#000000',
                        backgroundColor: gradient,
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#000000',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#000000',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                color: '#6B7280',
                                font: {
                                    size: 12,
                                    weight: '500'
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#F3F4F6',
                                drawBorder: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                color: '#6B7280',
                                font: {
                                    size: 12,
                                    weight: '500'
                                },
                                callback: function(value) {
                                    return '₦' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: '#000000',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#000000',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: false,
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 13
                            },
                            callbacks: {
                                label: function(context) {
                                    return 'Revenue: ₦' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverRadius: 8
                        }
                    }
                }
            });
        }).catch(error => {
            console.log('Chart data not available yet');
            // Show placeholder or empty state
        });
    });
</script>
        <?php $__env->stopPush(); ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/vendor/dashboard.blade.php ENDPATH**/ ?>