<?php

namespace App\Livewire\Wishlist;

use App\Models\Wishlist;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Index extends Component
{
    use WithPagination;

    public function removeFromWishlist($productId)
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        $user = Auth::user();
        $wishlistItem = $user->wishlist()->where('product_id', $productId)->first();

        if ($wishlistItem) {
            $wishlistItem->delete();
            $this->dispatch('toast', message: 'Product removed from wishlist.', type: 'success');
            $this->dispatch('wishlistUpdated');
        }
    }

    public function render()
    {
        $wishlistItems = collect();
        
        if (Auth::check()) {
            $wishlistItems = Auth::user()->wishlist()
                ->with(['product.vendor', 'product.category'])
                ->latest()
                ->paginate(12);
        }

        return view('livewire.wishlist.index', [
            'wishlistItems' => $wishlistItems,
        ]);
    }
}
