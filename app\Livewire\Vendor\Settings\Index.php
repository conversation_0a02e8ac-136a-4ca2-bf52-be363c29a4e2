<?php

namespace App\Livewire\Vendor\Settings;

use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;

#[Layout('layouts.vendor')]
class Index extends Component
{
    use WithFileUploads;

    public Vendor $vendor;

    #[Validate('required|string|max:255')]
    public $shop_name;

    #[Validate('required|string|max:255')]
    public $business_name;

    #[Validate('required|string|max:255')]
    public $business_address;

    #[Validate('required|string|max:255')]
    public $city;

    #[Validate('required|string|max:255')]
    public $state;

    #[Validate('required|string|max:255')]
    public $country;

    #[Validate('nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048')]
    public $logo;

    public function mount()
    {
        $this->vendor = Auth::user()->vendor;
        $this->shop_name = $this->vendor->shop_name;
        $this->business_name = $this->vendor->business_name;
        $this->business_address = $this->vendor->business_address;
        $this->city = $this->vendor->city;
        $this->state = $this->vendor->state;
        $this->country = $this->vendor->country;
    }

    public function updateSettings()
    {
        $this->validate();

        $validatedData = [
            'shop_name' => $this->shop_name,
            'business_name' => $this->business_name,
            'business_address' => $this->business_address,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
        ];

        if ($this->logo) {
            // Delete old logo if it exists
            if ($this->vendor->logo) {
                Storage::disk('filebase')->delete($this->vendor->logo);
            }
            $path = $this->logo->store('vendors/logos', 'filebase');
            $validatedData['logo'] = $path;
        }

        // Update slug if shop name changes
        if ($this->vendor->shop_name !== $this->shop_name) {
            $validatedData['slug'] = Str::slug($this->shop_name);
        }

        $this->vendor->update($validatedData);

        session()->flash('success', 'Settings updated successfully.');
        return $this->redirect(route('vendor.settings.index'), navigate: true);
    }

    public function render()
    {
        return view('livewire.vendor.settings.index');
    }
}
