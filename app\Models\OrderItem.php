<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;
    protected $fillable = [
        'order_id',
        'vendor_id',
        'product_id',
        'product_name',
        'quantity',
        'price',
        'unit_price',
        'subtotal',
        'status',
    ];
    
    /**
     * Boot function to handle model events
     */
    protected static function boot()
    {
        parent::boot();
        
        // Auto-calculate subtotal when saving
        static::saving(function ($orderItem) {
            if (empty($orderItem->unit_price)) {
                $orderItem->unit_price = $orderItem->price;
            }
            
            if (empty($orderItem->subtotal)) {
                $orderItem->subtotal = $orderItem->price * $orderItem->quantity;
            }
            
            if (empty($orderItem->product_name) && $orderItem->product) {
                $orderItem->product_name = $orderItem->product->name;
            }
        });
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
}
