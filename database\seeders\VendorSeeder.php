<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Vendor;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Str;

class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Nike vendor
        $this->createVendor(
            'Nike',
            'Leading sportswear and athletic footwear brand known for innovation and performance.',
            '1 Nike Store Road',
            'Ikeja',
            'Lagos',
            'Nigeria',
            '+2348012345678',
            'nike-logo.png',
            true
        );

        // Create Adidas vendor
        $this->createVendor(
            'Adidas',
            'Iconic sportswear brand combining style with performance for athletes and fashion enthusiasts.',
            '2 Adidas Avenue',
            'Lekki',
            'Lagos',
            'Nigeria',
            '+2348087654321',
            'adidas-logo.png',
            true
        );

        // Create Puma vendor
        $this->createVendor(
            'Puma',
            'Sports lifestyle brand offering innovative designs for both athletic and casual wear.',
            '3 Akin Adesola Street',
            'Victoria Island',
            'Lagos',
            'Nigeria',
            '+2348098765432',
            'puma-logo.png',
            true
        );
    }

    /**
     * Create a vendor with the given details
     */
    private function createVendor($name, $description, $address, $city, $state, $country, $phone, $logo, $featured = false)
    {
        // Create a user for the vendor if it doesn't exist
        // Get the vendor role
        $vendorRole = Role::where('name', 'vendor')->first();

        // Create a user for the vendor if it doesn't exist
        $email = Str::slug($name) . '@example.com';
        $user = User::firstOrCreate(
            ['email' => $email],
            [
                'name' => $name . ' Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role_id' => $vendorRole->id
            ]
        );

        // Create or update the vendor
        Vendor::updateOrCreate(
            ['user_id' => $user->id],
            [
                'shop_name' => $name,
                'slug' => Str::slug($name),
                'business_name' => $name,
                'business_address' => $address,
                'city' => $city,
                'state' => $state,
                'country' => $country,
                'phone' => $phone,
                'logo' => $logo,
                'is_approved' => true,
                'is_featured' => $featured
            ]
        );
    }
}
