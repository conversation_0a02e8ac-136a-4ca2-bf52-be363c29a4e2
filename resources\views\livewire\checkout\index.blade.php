<div class="bg-gray-50">
    <div class="max-w-2xl mx-auto pt-16 pb-24 px-4 sm:px-6 lg:max-w-7xl lg:px-8">
        <h2 class="sr-only">Checkout</h2>

        <div class="lg:grid lg:grid-cols-2 lg:gap-x-12 xl:gap-x-16">
            <!-- Shipping and Payment Information -->
            <div class="mt-10 lg:mt-0">
                <h2 class="text-lg font-medium text-gray-900">Shipping information</h2>

                <div class="mt-4 grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-4">
                    <div class="sm:col-span-2">
                        <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                        <input type="text" id="address" wire:model.defer="shippingAddress.address" placeholder="123 Main St" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                        @error('shippingAddress.address') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                        <input type="text" id="city" wire:model.defer="shippingAddress.city" placeholder="Anytown" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                        @error('shippingAddress.city') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label for="postal_code" class="block text-sm font-medium text-gray-700">Postal code</label>
                        <input type="text" id="postal_code" wire:model.defer="shippingAddress.postal_code" placeholder="12345" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                        @error('shippingAddress.postal_code') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-2">
                        <label for="state" class="block text-sm font-medium text-gray-700">State</label>
                        <select id="state" wire:model="shippingAddress.state" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                            <option value="">Select a state</option>
                            @foreach($states as $state => $lgas)
                                <option value="{{ $state }}">{{ $state }}</option>
                            @endforeach
                        </select>
                        @error('shippingAddress.state') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    @if(!empty($lgas))
                        <div class="sm:col-span-2">
                            <label for="lga" class="block text-sm font-medium text-gray-700">LGA</label>
                            <select id="lga" wire:model="shippingAddress.lga" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                                <option value="">Select an LGA</option>
                                @foreach($lgas as $lga)
                                    <option value="{{ $lga }}">{{ $lga }}</option>
                                @endforeach
                            </select>
                            @error('shippingAddress.lga') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                    @endif

                    <div class="sm:col-span-2">
                        <button type="button" wire:click="getShippingRates" wire:loading.attr="disabled" class="w-full bg-black text-white py-2 px-4 rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 disabled:opacity-50">
                            <span wire:loading.remove wire:target="getShippingRates">Get Shipping Rates</span>
                            <span wire:loading wire:target="getShippingRates">Loading...</span>
                        </button>
                    </div>
                    @error('shipping') <span class="sm:col-span-2 text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Shipping Method -->
                @if(!empty($shippingRates))
                    <div class="mt-10 border-t border-gray-200 pt-10">
                        <h2 class="text-lg font-medium text-gray-900">Shipping method</h2>

                        <fieldset class="mt-4">
                            <legend class="sr-only">Shipping method</legend>
                            <div class="space-y-4">
                                @foreach($shippingRates as $key => $rate)
                                    <label wire:click="selectShippingRate('{{ $key }}')" class="relative border rounded-lg p-4 flex cursor-pointer @if($selectedShippingRate && ($selectedShippingRate['courier_id'] ?? $selectedShippingRate['id']) == ($rate['courier_id'] ?? $rate['id'])) border-black @else border-gray-300 @endif">
                                        <input type="radio" name="shipping-method" value="{{ $key }}" class="sr-only">
                                        <div class="flex-1 flex">
                                            <div class="flex flex-col">
                                                <span class="block text-sm font-medium text-gray-900">{{ $rate['courier_name'] ?? $rate['name'] ?? 'Shipping Option' }}</span>
                                                <span class="mt-1 flex items-center text-sm text-gray-500">{{ $rate['description'] ?? 'Standard delivery' }}</span>
                                                <span class="mt-6 text-sm font-medium text-gray-900">₦{{ number_format($rate['total'] ?? $rate['fee'] ?? 0, 2) }}</span>
                                            </div>
                                        </div>
                                        @if($selectedShippingRate && ($selectedShippingRate['courier_id'] ?? $selectedShippingRate['id']) == ($rate['courier_id'] ?? $rate['id']))
                                            <svg class="h-5 w-5 text-black absolute top-4 right-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                        @endif
                                    </label>
                                @endforeach
                            </div>
                        </fieldset>
                    </div>
                @endif

                <!-- Payment -->
                <div class="mt-10 border-t border-gray-200 pt-10">
                    <h2 class="text-lg font-medium text-gray-900">Payment</h2>

                    @if($selectedShippingRate)
                        <div class="mt-6">
                            <button type="button" wire:click="proceedToPayment" class="w-full bg-black text-white py-3 px-4 rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2">
                                Proceed to Payment (₦{{ number_format($total, 2) }})
                            </button>
                        </div>
                    @else
                        <p class="mt-4 text-sm text-gray-500">Please select a shipping method to proceed.</p>
                    @endif
                </div>
            </div>

            <!-- Order summary -->
            <div class="mt-10 lg:mt-0">
                <h2 class="text-lg font-medium text-gray-900">Order summary</h2>

                <div class="mt-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <h3 class="sr-only">Items in your cart</h3>
                    <ul role="list" class="divide-y divide-gray-200">
                        @foreach($cartItems as $item)
                            <li class="flex py-6 px-4 sm:px-6">
                                <div class="flex-shrink-0">
                                    <img src="{{ $item['image_url'] ?? asset('img/default-product.png') }}" alt="{{ $item['name'] }}" class="w-20 rounded-md">
                                </div>

                                <div class="ml-6 flex-1 flex flex-col">
                                    <div class="flex">
                                        <div class="min-w-0 flex-1">
                                            <h4 class="text-sm">
                                                <a href="#" class="font-medium text-gray-700 hover:text-gray-800">{{ $item['name'] }}</a>
                                            </h4>
                                            <p class="mt-1 text-sm text-gray-500">Qty: {{ $item['quantity'] }}</p>
                                        </div>
                                    </div>

                                    <div class="flex-1 flex items-end justify-between">
                                        <p class="mt-1 text-sm font-medium text-gray-900">@currency($item['price'] * $item['quantity'])</p>
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                    <dl class="border-t border-gray-200 py-6 px-4 space-y-6 sm:px-6">
                        <div class="flex items-center justify-between">
                            <dt class="text-sm">Subtotal</dt>
                            <dd class="text-sm font-medium text-gray-900">@currency($subtotal)</dd>
                        </div>
                        <div class="flex items-center justify-between">
                            <dt class="text-sm">Shipping</dt>
                            <dd class="text-sm font-medium text-gray-900">@currency($shippingCost)</dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-6">
                            <dt class="text-base font-medium">Total</dt>
                            <dd class="text-base font-medium text-gray-900">@currency($total)</dd>
                        </div>
                    </dl>

                    <div class="border-t border-gray-200 py-6 px-4 sm:px-6">
                        <x-button label="Place order" primary class="w-full" disabled />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
