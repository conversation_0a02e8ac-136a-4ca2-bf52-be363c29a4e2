<?php

namespace App\Livewire\Admin\Products;

use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use Illuminate\Support\Str;
use Livewire\Component;

class ProductForm extends Component
{
    public ?Product $product = null;
    public string $name = '';
    public string $slug = '';
    public ?int $vendor_id = null;
    public ?int $category_id = null;
    public string $description = '';
    public ?float $price = null;
    public ?float $discount_price = null;
    public ?string $image_url = '';
    public ?int $stock = null;
    public bool $is_active = true;

    public array $vendors = [];
    public array $categories = [];

    public function mount(?Product $product = null): void
    {
        $this->product = $product;
        if ($this->product && $this->product->exists) {
            $this->name = $this->product->name;
            $this->slug = $this->product->slug;
            $this->vendor_id = $this->product->vendor_id;
            $this->category_id = $this->product->category_id;
            $this->description = $this->product->description;
            $this->price = $this->product->price;
            $this->discount_price = $this->product->discount_price;
            $this->image_url = $this->product->image_url;
            $this->stock = $this->product->stock;
            $this->is_active = $this->product->is_active;
        }
        $this->vendors = Vendor::orderBy('shop_name')->get(['id', 'shop_name'])->toArray();
        $this->categories = Category::orderBy('name')->get(['id', 'name'])->toArray();
    }

    protected function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:products,slug,' . ($this->product?->id ?? 'NULL'),
            'vendor_id' => 'required|exists:vendors,id',
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'image_url' => 'nullable|url',
            'stock' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ];
    }

    public function updatedName(string $value): void
    {
        if (!$this->product || !$this->product->exists) {
            $this->slug = Str::slug($value);
        }
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'slug' => $this->slug,
            'vendor_id' => $this->vendor_id,
            'category_id' => $this->category_id,
            'description' => $this->description,
            'price' => $this->price,
            'discount_price' => $this->discount_price,
            'image_url' => $this->image_url,
            'stock' => $this->stock,
            'is_active' => $this->is_active,
        ];

        if ($this->product && $this->product->exists) {
            $this->product->update($data);
            session()->flash('success', 'Product updated successfully.');
        } else {
            Product::create($data);
            session()->flash('success', 'Product created successfully.');
        }

        return redirect()->route('admin.products.index');
    }

    public function render()
    {
        return view('livewire.admin.products.product-form')->layout('layouts.admin');
    }
}
