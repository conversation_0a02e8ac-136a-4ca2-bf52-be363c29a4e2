<?php

namespace App\Livewire;

use Livewire\Component;

use App\Models\Product;
use WireUi\Traits\WireUiActions;

class AddToCartButton extends Component
{
    use WireUiActions;

    public Product $product;
    public bool $wasAdded = false;

    public function addToCart()
    {
        $cart = session()->get('cart', []);

        // Use discounted price if available
        $price = $this->product->discounted_price ?? $this->product->price;

        if(isset($cart[$this->product->id])) {
            $cart[$this->product->id]['quantity']++;
        } else {
            $cart[$this->product->id] = [
                "name" => $this->product->name,
                "quantity" => 1,
                "price" => $price,
                "image" => $this->product->image_url ?? asset('storage/product-placeholder.jpg')
            ];
        }

        session()->put('cart', $cart);

        $this->wasAdded = true;

        // Dispatch events to update the cart counter in the header
        $this->dispatch('cartUpdated');
        $this->dispatch('cart-updated-total', count($cart));

        // Show a success notification
        $this->notification()->success(
            'Added to cart!',
            "{$this->product->name} has been added to your cart."
        );
    }

    public function render()
    {
        return view('livewire.add-to-cart-button');
    }
}
