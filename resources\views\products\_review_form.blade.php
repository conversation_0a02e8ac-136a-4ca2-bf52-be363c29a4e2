@auth
<form action="{{ route('products.reviews.store', $product->slug) }}" method="POST" class="mt-4 p-4 border rounded-3 bg-light">
    @csrf
    <h4 class="mb-3">Leave a Review</h4>
    <div class="mb-3">
        <label for="rating" class="form-label">Rating</label>
        <div class="rating-stars">
            @for ($i = 5; $i >= 1; $i--)
            <input type="radio" name="rating" id="rating-{{ $i }}" value="{{ $i }}" required @if(old('rating') == $i) checked @endif>
            <label for="rating-{{ $i }}"><i class="fas fa-star"></i></label>
            @endfor
        </div>
        @error('rating')
            <span class="text-danger d-block mt-1">{{ $message }}</span>
        @enderror
    </div>
    <div class="mb-3">
        <label for="comment" class="form-label">Comment</label>
        <textarea name="comment" id="comment" rows="4" class="w-full px-4 py-3 text-gray-800 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600" required>{{ old('comment') }}</textarea>
        @error('comment')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>
    <button type="submit" class="px-4 py-2 bg-black text-white font-semibold rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors">Submit Review</button>
</form>
@else
<p class="mt-4">Please <a href="{{ route('login') }}">log in</a> to leave a review.</p>
@endauth
