<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShipBubbleService
{
    protected $apiKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.shipbubble.key');
        $this->baseUrl = config('services.shipbubble.url', 'https://api.shipbubble.com/v1');
    }

    public function getRates(array $data)
    {
        return $this->sendRequest('post', '/shipping/fetch_rates', $data);
    }

    public function createShipment(array $data)
    {
        return $this->sendRequest('post', '/shipping/labels', $data);
    }

    public function validateAddress(array $data)
    {
        // Build a simple, clean address string using only the essential components.
        $addressParts = [
            $data['address'] ?? null,
            $data['city'] ?? null,
            $data['state'] ?? null,
        ];

        // Filter out any null or empty parts.
        $filteredParts = array_filter($addressParts);

        // Join the parts into a single, comma-separated string.
        $addressString = implode(', ', $filteredParts);

        // Append the country for clarity, as the API seems to require it.
        $addressString .= ', Nigeria';

        // Sanitize the final address string to remove any characters the API might reject.
        $sanitizedAddress = preg_replace('/[^\p{L}\p{N}\s,]/u', '', $addressString);

        // Final cleanup to remove potential duplicate commas or leading/trailing commas/spaces.
        $sanitizedAddress = trim(preg_replace('/,{2,}/', ',', $sanitizedAddress), ' ,');

        // Construct the final payload with the clean address.
        $payload = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'address' => $sanitizedAddress,
        ];

        return $this->sendRequest('post', '/shipping/address/validate', $payload);
    }

    /**
     * Track a shipment using Shipbubble API
     *
     * @param string $trackingCode
     * @return array
     */
    public function trackShipment(string $trackingCode)
    {
        return $this->sendRequest('get', "/tracking/{$trackingCode}");
    }

    /**
     * Get shipping rates for cart items and address
     * This method formats cart data for Shipbubble API
     *
     * @param array $cartItems
     * @param array $shippingAddress
     * @return array
     */
    public function getShippingRates(array $cartItems, array $shippingAddress)
    {
        // First, we need to validate/create addresses
        $senderAddress = $this->getOrCreateSenderAddress();
        $receiverAddress = $this->getOrCreateReceiverAddress($shippingAddress);

        if (!$senderAddress || !$receiverAddress) {
            throw new \Exception('Failed to validate addresses');
        }

        // Format package items for Shipbubble
        $packageItems = [];
        $totalWeight = 0;
        $totalAmount = 0;

        foreach ($cartItems as $item) {
            $weight = ($item['weight'] ?? 0.5) / 1000; // Convert grams to kg, default 500g
            $amount = $item['price'] ?? 0;
            $quantity = $item['quantity'] ?? 1;

            $packageItems[] = [
                'name' => $item['name'] ?? 'Product',
                'description' => $item['description'] ?? 'Product description',
                'unit_weight' => (string)$weight,
                'unit_amount' => (string)$amount,
                'quantity' => (string)$quantity,
            ];

            $totalWeight += $weight * $quantity;
            $totalAmount += $amount * $quantity;
        }

        // Calculate package dimensions (you may want to make this configurable)
        $packageDimension = [
            'length' => 30, // cm
            'width' => 20,  // cm
            'height' => 15, // cm
        ];

        $rateData = [
            'sender_address_code' => $senderAddress['code'],
            'reciever_address_code' => $receiverAddress['code'],
            'pickup_date' => now()->addDay()->format('Y-m-d'), // Next day pickup
            'category_id' => 1, // General category, you may want to make this configurable
            'package_items' => $packageItems,
            'package_dimension' => $packageDimension,
            'delivery_instructions' => 'Handle with care',
        ];

        return $this->getRates($rateData);
    }

    /**
     * Get or create sender address (business address)
     *
     * @return array|null
     */
    protected function getOrCreateSenderAddress()
    {
        // For now, return a mock address code
        // In production, you should validate/create the business address
        return ['code' => 1]; // This should be the actual address code from Shipbubble
    }

    /**
     * Get or create receiver address from shipping address
     *
     * @param array $shippingAddress
     * @return array|null
     */
    protected function getOrCreateReceiverAddress(array $shippingAddress)
    {
        $addressData = [
            'name' => $shippingAddress['name'] ?? 'Customer',
            'email' => $shippingAddress['email'] ?? '<EMAIL>',
            'phone' => $shippingAddress['phone'] ?? '***********',
            'address' => $shippingAddress['address'] ?? '',
            'city' => $shippingAddress['city'] ?? '',
            'state' => $shippingAddress['state'] ?? '',
            'country' => $shippingAddress['country'] ?? 'NG',
        ];

        $result = $this->validateAddress($addressData);

        if (isset($result['status']) && $result['status'] === 'success') {
            return ['code' => $result['data']['address_code'] ?? 2];
        }

        // Return a mock address code if validation fails
        return ['code' => 2];
    }

    protected function sendRequest(string $method, string $endpoint, array $data = [])
    {
        Log::info('ShipBubble API Request:', [
            'endpoint' => $endpoint,
            'payload' => $data
        ]);

        try {
            $response = Http::withToken($this->apiKey)
                ->acceptJson()
                ->{$method}($this->baseUrl . $endpoint, $data);

            if ($response->failed()) {
                Log::error('ShipBubble API Error:', [
                    'endpoint' => $endpoint,
                    'status' => $response->status(),
                    'response' => $response->json() ?? $response->body()
                ]);
                return [
                    'status' => 'error',
                    'message' => 'ShipBubble API request failed.',
                    'details' => $response->json() ?? $response->body(),
                ];
            }

            Log::info('ShipBubble API Response:', [
                'endpoint' => $endpoint,
                'response' => $response->json()
            ]);

            return $response->json();

        } catch (\Exception $e) {
            Log::critical('ShipBubble Service Exception:', [
                'endpoint' => $endpoint,
                'message' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'An unexpected error occurred.',
                'details' => $e->getMessage()
            ];
        }
    }
}
