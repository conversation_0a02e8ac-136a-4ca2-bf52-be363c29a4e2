<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Product Management
                </h1>
                <p class="text-gray-300 text-lg">Manage your store inventory and listings</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.products.create') }}"
                   class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center space-x-2">
                    <i class="fa-solid fa-plus transition-transform duration-300 group-hover:rotate-90"></i>
                    <span>Add Product</span>
                </a>
                <button class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-download transition-transform duration-300 group-hover:bounce"></i>
                    <span>Export</span>
                </button>
            </div>
        </div>
    </div>

    {{-- Success Message --}}
    @if (session()->has('success'))
        <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-8 rounded-r-xl shadow-lg">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <p class="text-green-800 font-medium">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    {{-- Modern Products Grid --}}
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        @forelse ($products as $product)
            <div class="group p-6 border-b border-gray-100 hover:bg-gray-50 transition-all duration-300 last:border-b-0">
                <div class="flex items-center justify-between">
                    {{-- Product Info --}}
                    <div class="flex items-center space-x-6 flex-1">
                        {{-- Product Image --}}
                        <div class="relative">
                            <img src="{{ $product->image_url }}"
                                 alt="{{ $product->name }}"
                                 class="w-16 h-16 rounded-xl object-cover shadow-lg group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute -top-2 -right-2 w-6 h-6 {{ $product->is_active ? 'bg-green-500' : 'bg-red-500' }} rounded-full flex items-center justify-center">
                                <i class="fas {{ $product->is_active ? 'fa-check' : 'fa-times' }} text-white text-xs"></i>
                            </div>
                        </div>

                        {{-- Product Details --}}
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between">
                                <div class="space-y-1">
                                    <h3 class="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300 truncate">
                                        {{ $product->name }}
                                    </h3>
                                    <p class="text-sm text-gray-500">
                                        <i class="fas fa-tag mr-1"></i>
                                        {{ $product->category->name ?? 'Uncategorized' }}
                                    </p>
                                    <div class="flex items-center space-x-4">
                                        <span class="text-xl font-bold text-gray-900">
                                            @currency($product->price)
                                        </span>
                                        @if($product->discount_price)
                                            <span class="text-sm text-gray-500 line-through">
                                                @currency($product->discount_price)
                                            </span>
                                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded-full">
                                                Sale
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Actions --}}
                    <div class="flex items-center space-x-3">
                        {{-- Status Badge --}}
                        <div class="text-center">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            <p class="text-xs text-gray-500 mt-1">
                                Created {{ $product->created_at->format('M d, Y') }}
                            </p>
                        </div>

                        {{-- Action Buttons --}}
                        <div class="flex items-center space-x-2">
                            <a href="{{ route('vendor.products.edit', $product->id) }}"
                               class="group p-3 bg-blue-50 text-blue-600 rounded-xl hover:bg-blue-100 hover:scale-105 transition-all duration-300"
                               title="Edit Product">
                                <i class="fas fa-edit group-hover:scale-110 transition-transform duration-300"></i>
                            </a>

                            <button wire:click="deleteProduct({{ $product->id }})"
                                    wire:confirm="Are you sure you want to delete this product? This action cannot be undone."
                                    class="group p-3 bg-red-50 text-red-600 rounded-xl hover:bg-red-100 hover:scale-105 transition-all duration-300"
                                    title="Delete Product">
                                <i class="fas fa-trash group-hover:scale-110 transition-transform duration-300"></i>
                            </button>

                            <div class="relative group">
                                <button class="p-3 bg-gray-50 text-gray-600 rounded-xl hover:bg-gray-100 hover:scale-105 transition-all duration-300"
                                        title="More Options">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            {{-- Empty State --}}
            <div class="text-center py-20">
                <div class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-box-open text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">No Products Yet</h3>
                <p class="text-gray-500 mb-8 max-w-md mx-auto">
                    Start building your store by adding your first product. You can add images, descriptions, pricing, and more.
                </p>
                <a href="{{ route('vendor.products.create') }}"
                   class="group inline-flex items-center px-6 py-3 bg-black text-white rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg">
                    <i class="fas fa-plus mr-2 transition-transform duration-300 group-hover:rotate-90"></i>
                    <span>Add Your First Product</span>
                </a>
            </div>
        @endforelse

        {{-- Pagination --}}
        @if ($products->hasPages())
            <div class="p-6 border-t border-gray-100 bg-gray-50">
                {{ $products->links() }}
            </div>
        @endif
    </div>
</div>
