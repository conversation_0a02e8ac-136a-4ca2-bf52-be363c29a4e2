<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('shipping_courier_id')->nullable()->after('shipping_method');
            $table->string('shipping_service_code')->nullable()->after('shipping_courier_id');
            $table->string('shipping_tracking_url')->nullable()->after('shipbubble_token');
            $table->string('shipping_provider_order_id')->nullable()->after('shipping_tracking_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['shipping_courier_id', 'shipping_service_code', 'shipping_tracking_url', 'shipping_provider_order_id']);
        });
    }
};
