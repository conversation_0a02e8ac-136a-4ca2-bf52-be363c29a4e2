document.addEventListener('DOMContentLoaded', function () {
    const searchInput = document.getElementById('ajaxSearchInput');
    const searchResultsContainer = document.getElementById('ajaxSearchResults');
    const searchForm = document.getElementById('ajaxSearchForm');
    let debounceTimer;

    if (searchInput && searchResultsContainer && searchForm) {
        searchInput.addEventListener('input', function () {
            clearTimeout(debounceTimer);
            const query = this.value.trim();

            if (query.length >= 2) { // Start searching after 2 characters
                searchResultsContainer.innerHTML = '<div class="p-3 text-center text-muted small">Searching...</div>';
                searchResultsContainer.style.display = 'block';
                debounceTimer = setTimeout(() => {
                    fetchSearchResults(query);
                }, 300); // Debounce time: 300ms
            } else {
                searchResultsContainer.style.display = 'none';
                searchResultsContainer.innerHTML = '';
            }
        });

        // Prevent form submission if search input is active and results are shown
        searchForm.addEventListener('submit', function(event) {
            if (searchInput.value.trim().length > 0 && searchResultsContainer.style.display === 'block') {
                // If user explicitly submits, let them go to the full search results page
                // We don't preventDefault here to allow standard form submission for a dedicated search page.
            }
        });

        // Hide results when clicking outside
        document.addEventListener('click', function (event) {
            if (!searchForm.contains(event.target)) {
                searchResultsContainer.style.display = 'none';
            }
        });

        // Show results if input is focused and has content (e.g., on re-focus)
        searchInput.addEventListener('focus', function() {
            if (this.value.trim().length >= 2 && searchResultsContainer.innerHTML !== '') {
                searchResultsContainer.style.display = 'block';
            }
        });

    }

    function fetchSearchResults(query) {
        const searchUrl = searchForm.action; // Get URL from form action
        
        fetch(`${searchUrl}?query=${encodeURIComponent(query)}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            displaySearchResults(data.products, query);
        })
        .catch(error => {
            console.error('Search error:', error);
            searchResultsContainer.innerHTML = '<div class="p-3 text-center text-danger small">Error loading results.</div>';
            searchResultsContainer.style.display = 'block';
        });
    }

    function displaySearchResults(products, query) {
        searchResultsContainer.innerHTML = ''; // Clear previous results

        if (products && products.length > 0) {
            const ul = document.createElement('ul');
            ul.className = 'list-group list-group-flush';

            products.forEach(product => {
                const li = document.createElement('li');
                li.className = 'list-group-item list-group-item-action';
                
                const link = document.createElement('a');
                link.href = product.show_url; // Using the pre-generated URL
                link.className = 'text-decoration-none text-dark d-flex align-items-center';

                let imgHtml = '';
                if (product.image_url) {
                    imgHtml = `<img src="${product.image_url}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover; margin-right: 10px; border-radius: 4px;">`;
                } else {
                    imgHtml = `<div style="width: 40px; height: 40px; background-color: #f0f0f0; margin-right: 10px; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 0.8em;">NoImg</div>`;
                }

                link.innerHTML = `
                    ${imgHtml}
                    <div>
                        <div class="fw-medium">${product.name}</div>
                        <small class="text-muted">${product.category_name || 'N/A'} - Price: $${parseFloat(product.price).toFixed(2)}</small>
                    </div>
                `;
                li.appendChild(link);
                ul.appendChild(li);
            });
            searchResultsContainer.appendChild(ul);

            // 'View all results' link
            const viewAllLink = document.createElement('a');
            viewAllLink.href = `${searchForm.action}?query=${encodeURIComponent(query)}`;
            viewAllLink.className = 'list-group-item list-group-item-action text-center text-primary fw-medium p-2';
            viewAllLink.textContent = 'View all results';
            searchResultsContainer.appendChild(viewAllLink);

        } else {
            searchResultsContainer.innerHTML = `<div class="p-3 text-center text-muted small">No products found for "${escapeHTML(query)}".</div>`;
        }
        searchResultsContainer.style.display = 'block';
    }

    function escapeHTML(str) {
        return str.replace(/[&<>'"/]/g, function (match) {
            const escape = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                "'": '&#39;',
                '"': '&quot;',
                '/': '&#x2F;'
            };
            return escape[match];
        });
    }

});
