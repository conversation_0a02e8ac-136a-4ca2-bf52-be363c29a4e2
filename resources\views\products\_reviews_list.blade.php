@if ($reviews->count() > 0)
    <h4 class="mb-3">Customer Reviews</h4>
    @foreach ($reviews as $review)
        <div class="review d-flex mb-4">
            <div class="flex-shrink-0 me-3">
                <img src="{{ $review->user->profile_photo_url ?? asset('images/default-avatar.png') }}" alt="{{ $review->user->name }}" class="rounded-circle" width="50" height="50">
            </div>
            <div class="flex-grow-1">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ $review->user->name }}</h6>
                    <div class="text-muted small">{{ $review->created_at->format('M d, Y') }}</div>
                </div>
                <div class="rating-stars mb-2">
                    @for ($i = 1; $i <= 5; $i++)
                        <i class="fas fa-star {{ $i <= $review->rating ? 'text-warning' : 'text-muted' }}"></i>
                    @endfor
                </div>
                <p class="mb-0">{{ $review->comment }}</p>
            </div>
        </div>
    @endforeach

    <div class="d-flex justify-content-center mt-4">
        {{ $reviews->links() }}
    </div>
@else
    <p>No reviews yet. Be the first to review this product!</p>
@endif
