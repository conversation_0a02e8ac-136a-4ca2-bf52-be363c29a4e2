<?php

namespace App\Livewire\Admin\Brands;

use App\Models\Brand;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use WireUi\Traits\WireUiActions;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithFileUploads, WithPagination, WireUiActions;

    public ?Brand $brand = null;
    public $logo;
    public bool $showModal = false;

    public string $name = '';
    public string $description = '';
    public bool $is_active = true;
    public bool $is_featured = false;

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:brands,name,' . ($this->brand?->id ?? ''),
            'description' => 'nullable|string',
            'logo' => 'nullable|image|max:2048',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ];
    }

    public function create()
    {
        $this->resetValidation();
        $this->reset('name', 'description', 'logo', 'is_active', 'is_featured', 'brand');
        $this->brand = new Brand();
        $this->showModal = true;
    }

    public function edit(Brand $brand)
    {
        $this->resetValidation();
        $this->brand = $brand;
        $this->name = $brand->name;
        $this->description = $brand->description;
        $this->is_active = $brand->is_active;
        $this->is_featured = $brand->is_featured;
        $this->logo = null;
        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'slug' => Str::slug($this->name),
            'description' => $this->description,
            'is_active' => $this->is_active,
            'is_featured' => $this->is_featured,
        ];

        if ($this->logo) {
            if ($this->brand->logo) {
                $oldPath = str_replace(Storage::disk('filebase')->url(''), '', $this->brand->logo);
                Storage::disk('filebase')->delete($oldPath);
            }
            $path = $this->logo->store('brand-logos', 'filebase');
            $data['logo'] = Storage::disk('filebase')->url($path);
        }

        if ($this->brand->exists) {
            $this->brand->update($data);
            $this->notification()->success('Brand updated successfully.');
        } else {
            Brand::create($data);
            $this->notification()->success('Brand created successfully.');
        }

        $this->showModal = false;
    }

    public function delete(Brand $brand)
    {
        if ($brand->products()->count() > 0) {
            $this->notification()->error('Cannot delete brand with associated products.');
            return;
        }

        if ($brand->logo) {
            $oldPath = str_replace(Storage::disk('filebase')->url(''), '', $brand->logo);
            Storage::disk('filebase')->delete($oldPath);
        }

        $brand->delete();
        $this->notification()->success('Brand deleted successfully.');
    }

    public function toggleFeatured(Brand $brand)
    {
        $brand->update(['is_featured' => !$brand->is_featured]);
        $this->notification()->success('Featured status updated.');
    }

    public function render()
    {
        return view('livewire.admin.brands.index', [
            'brands' => Brand::withCount('products')->latest()->paginate(10),
        ]);
    }
}
