@extends('layouts.admin')

@section('title', 'Admin Dashboard')

@section('content')
<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">Dashboard</h1>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Sales -->
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 flex items-center justify-between border-l-4 border-black">
            <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase">Total Sales</p>
                <p class="text-3xl font-bold text-gray-900 dark:text-white">₦{{ number_format($totalSales, 2) }}</p>
            </div>
            <div class="text-gray-400 dark:text-gray-500">
                <x-icon name="banknotes" class="w-12 h-12" />
            </div>
        </div>

        <!-- Total Orders -->
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 flex items-center justify-between border-l-4 border-black">
            <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase">Total Orders</p>
                <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $totalOrders }}</p>
            </div>
            <div class="text-gray-400 dark:text-gray-500">
                <x-icon name="shopping-cart" class="w-12 h-12" />
            </div>
        </div>

        <!-- Total Customers -->
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 flex items-center justify-between border-l-4 border-black">
            <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase">Total Customers</p>
                <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $totalCustomers }}</p>
            </div>
            <div class="text-gray-400 dark:text-gray-500">
                <x-icon name="users" class="w-12 h-12" />
            </div>
        </div>

        <!-- Total Vendors -->
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 flex items-center justify-between border-l-4 border-black">
            <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase">Total Vendors</p>
                <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $totalVendors }}</p>
            </div>
            <div class="text-gray-400 dark:text-gray-500">
                <x-icon name="building-storefront" class="w-12 h-12" />
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Pending Vendors -->
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Pending Vendor Approvals</h3>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700/50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Vendor Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                                <th scope="col" class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @forelse ($pendingVendors as $vendor)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $vendor->shop_name }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $vendor->user->email }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="{{ route('admin.vendors.show', $vendor->id) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">View</a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="px-6 py-12 text-center text-sm text-gray-500 dark:text-gray-400">No pending vendors.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Orders</h3>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700/50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Order ID</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Customer</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @forelse ($recentOrders as $order)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:underline">
                                        <a href="{{ route('admin.orders.show', $order->id) }}">{{ $order->order_number }}</a>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ $order->user->name }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">₦{{ number_format($order->total, 2) }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $order->status_class }}">
                                            {{ $order->status }}
                                        </span>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="px-6 py-12 text-center text-sm text-gray-500 dark:text-gray-400">No recent orders.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
