<?php

namespace Database\Factories;

use App\Models\Brand;
 use Illuminate\Database\Eloquent\Factories\Factory;

 /**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
     public function definition(): array
     {
-        return [
-            //
+        return [
+            'name' => $this->faker->words(3, true),
+            'description' => $this->faker->paragraph,
+            'price' => $this->faker->randomFloat(2, 10, 1000), // Price between 10.00 and 1000.00
+            'image_url' => $this->faker->imageUrl(640, 480, 'products', true), // Generate a placeholder image URL
+            // Assign a random existing brand_id. Assumes brands are seeded first.
+            // If no brands exist, this might cause issues or assign null if the relationship allows.
+            'brand_id' => Brand::inRandomOrder()->first()?->id ?? null,
         ];
     }
 }
