<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vendor_id')->constrained()->onDelete('cascade');
            $table->string('type'); // e.g., 'sale', 'commission', 'withdrawal', 'refund', 'adjustment'
            $table->decimal('amount', 10, 2); // Can be positive (credit) or negative (debit)
            $table->decimal('balance_after', 10, 2);
            $table->string('description');
            $table->morphs('reference'); // For Order, Withdrawal, etc.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_transactions');
    }
};
