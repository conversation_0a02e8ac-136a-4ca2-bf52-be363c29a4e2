document.addEventListener('DOMContentLoaded', () => {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Function to show a dismissible alert
    const showAlert = (message, type = 'success') => {
        const alertContainer = document.getElementById('alert-container');
        if (!alertContainer) {
            console.error('Alert container not found');
            return;
        }
        const alert = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        alertContainer.innerHTML = alert;
    };

    // Function to update cart totals in the UI
    const updateCartTotals = (subtotal, tax, total) => {
        document.getElementById('cart-subtotal').textContent = `₦${parseFloat(subtotal).toFixed(2)}`;

        document.getElementById('cart-total').textContent = `₦${parseFloat(total).toFixed(2)}`;
    };

    // Function to update the cart view (e.g., show/hide empty cart message)
    const updateCartView = (count) => {
        const cartItemsContainer = document.getElementById('cart-items-container');
        const emptyCartContainer = document.getElementById('empty-cart-container');
        const cartBadge = document.querySelector('.cart-badge');

        if (count > 0) {
            if(cartItemsContainer) cartItemsContainer.style.display = 'block';
            if(emptyCartContainer) emptyCartContainer.style.display = 'none';
        } else {
            if(cartItemsContainer) cartItemsContainer.style.display = 'none';
            if(emptyCartContainer) emptyCartContainer.style.display = 'block';
        }

        if (cartBadge) {
            cartBadge.textContent = count;
            cartBadge.style.display = count > 0 ? 'inline-block' : 'none';
        }
    };

    // --- Event Listeners ---

    // Handle quantity updates
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            const form = this.closest('.cart-update-form');
            if (form) {
                form.dispatchEvent(new Event('submit', { cancelable: true }));
            }
        });
    });

    // Handle cart update form submission (AJAX)
    document.querySelectorAll('.cart-update-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const itemId = this.dataset.itemId;

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Cart updated successfully.');
                    updateCartTotals(data.cart.subtotal, 0, data.cart.total);
                    
                    const itemSubtotalEl = document.querySelector(`.item-subtotal[data-item-id="${itemId}"]`);
                    if(itemSubtotalEl) {
                        itemSubtotalEl.textContent = `₦${parseFloat(data.cart.item_subtotal).toFixed(2)}`;
                    }
                } else {
                    showAlert(data.message || 'Failed to update cart.', 'danger');
                }
            })
            .catch(error => {
                console.error('Error updating cart:', error);
                showAlert('An error occurred while updating the cart.', 'danger');
            });
        });
    });

    // Handle item removal (AJAX)
    document.querySelectorAll('.cart-remove-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const removeButton = this.querySelector('.cart-remove-button');
            removeButton.disabled = true;

            fetch(this.action, {
                method: 'POST', // Laravel uses POST for DELETE with _method field
                body: new FormData(this),
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Item removed successfully.');
                    this.closest('.cart-item-row').remove();
                    updateCartTotals(data.cart.subtotal, 0, data.cart.total);
                    updateCartView(data.cart.count);
                } else {
                    showAlert(data.message || 'Failed to remove item.', 'danger');
                    removeButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error removing item:', error);
                showAlert('An error occurred while removing the item.', 'danger');
                removeButton.disabled = false;
            });
        });
    });

    // Handle clear cart (AJAX)
    const clearCartForm = document.getElementById('clear-cart-form');
    if(clearCartForm) {
        clearCartForm.addEventListener('submit', function(e) {
            e.preventDefault();
            if(!confirm('Are you sure you want to clear your entire cart?')) {
                return;
            }

            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Cart cleared successfully.');
                    updateCartTotals(0, 0, 0);
                    updateCartView(0);
                } else {
                    showAlert(data.message || 'Failed to clear cart.', 'danger');
                }
            })
            .catch(error => {
                console.error('Error clearing cart:', error);
                showAlert('An error occurred while clearing the cart.', 'danger');
            });
        });
    }

    // Handle Add to Cart for recommended products
    document.querySelectorAll('.ajax-add-to-cart-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const button = this.querySelector('button[type="submit"]');
            const originalButtonContent = button.innerHTML;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
            button.disabled = true;

            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json().then(data => ({ ok: response.ok, data })))
            .then(({ ok, data }) => {
                if (ok) {
                    showAlert(data.message || 'Product added to cart!', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showAlert(data.message || 'Could not add product to cart.', 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred.', 'danger');
            })
            .finally(() => {
                button.innerHTML = originalButtonContent;
                button.disabled = false;
            });
        });
    });
});
