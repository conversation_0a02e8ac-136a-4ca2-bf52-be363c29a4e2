<?php

namespace App\Livewire\Checkout;

use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.app')]
class Success extends Component
{
    public $orders;
    public $transaction_id;

    public function mount($transaction_id = null)
    {
        $this->transaction_id = $transaction_id ?? request()->query('transaction_id');

        if (!$this->transaction_id) {
            return redirect()->route('home');
        }

        $this->orders = Order::where('checkout_transaction_id', $this->transaction_id)
            ->where('user_id', Auth::id())
            ->with('vendor', 'items.product')
            ->get();

        if ($this->orders->isEmpty()) {
            return redirect()->route('home')->with('error', 'No order found.');
        }
    }

    public function render()
    {
        return view('livewire.checkout.success');
    }
}
