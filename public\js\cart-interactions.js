document.addEventListener('DOMContentLoaded', function () {
    const cartForms = document.querySelectorAll('.ajax-add-to-cart-form');

    cartForms.forEach(form => {
        form.addEventListener('submit', function (event) {
            event.preventDefault();

            const button = form.querySelector('.add-to-cart-btn');
            const originalButtonContent = button.innerHTML;
            const cartCountIndicator = document.querySelector('.cart-count-indicator');

            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

            const formData = new FormData(form);
            const url = form.action;

            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': formData.get('_token')
                }
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw new Error(err.message || 'An unknown error occurred.'); });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    if (cartCountIndicator && data.cart && typeof data.cart.count !== 'undefined') {
                        cartCountIndicator.textContent = data.cart.count;
                        cartCountIndicator.classList.remove('d-none');
                    }
                    button.innerHTML = '<i class="fas fa-check"></i>';
                } else {
                    showToast(data.message || 'Could not add product to cart.', 'error');
                }
            })
            .catch(error => {
                showToast(error.message, 'error');
            })
            .finally(() => {
                setTimeout(() => {
                    button.innerHTML = originalButtonContent;
                    button.disabled = false;
                }, 1500);
            });
        });
    });

    function showToast(message, type = 'info') {
        const container = document.getElementById('toast-container') || createToastContainer();
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : 'success'} border-0 show`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        container.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
        bsToast.show();
        toast.addEventListener('hidden.bs.toast', () => toast.remove());
    }

    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    }
});
