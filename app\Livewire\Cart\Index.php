<?php

namespace App\Livewire\Cart;

use App\Models\Product;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.app')]
class Index extends Component
{
    /** @var \Illuminate\Support\Collection */
    public $cartItems;
    public $subtotal = 0;
    public $total = 0;
    /** @var \Illuminate\Support\Collection */
    public $recommendedProducts;

    protected $listeners = ['cartUpdated' => 'updateCart'];

    public function mount()
    {
        $this->cartItems = collect();
        $this->recommendedProducts = collect();
        $this->updateCart();
    }

    public function updateCart()
    {
        $cart = session('cart', []);
        $cartWithIds = collect($cart)->map(function($item, $id) {
            $item['id'] = $id;
            return $item;
        });
        $this->cartItems = $cartWithIds;
        $this->calculateTotals();
        $this->loadRecommendedProducts();
    }

    public function updateQuantity($productId, $quantity)
    {
        $quantity = (int)$quantity;
        $cart = session('cart', []);

        if (isset($cart[$productId])) {
            if ($quantity > 0) {
                $cart[$productId]['quantity'] = $quantity;
            } else {
                unset($cart[$productId]);
            }
            session()->put('cart', $cart);
            $this->updateCart();
            $this->dispatch('cart-updated-total', count($cart)); // For header count
        }
    }

    public function removeItem($productId)
    {
        $cart = session('cart', []);
        unset($cart[$productId]);
        session()->put('cart', $cart);
        $this->updateCart();
        $this->dispatch('cart-updated-total', count($cart)); // For header count
    }

    public function clearCart()
    {
        session()->forget('cart');
        $this->updateCart();
        $this->dispatch('cart-updated-total', 0); // For header count
    }

    private function calculateTotals()
    {
        $this->subtotal = $this->cartItems->sum(function ($item) {
            return $item['price'] * $item['quantity'];
        });
        $this->total = $this->subtotal; // Assuming no tax/shipping for now
    }

    private function loadRecommendedProducts()
    {
        if ($this->cartItems->isEmpty()) {
            $this->recommendedProducts = Product::where('is_active', true)
                ->inRandomOrder()
                ->take(4)
                ->get();
            return;
        }

        $productIds = $this->cartItems->pluck('id')->toArray();
        $relatedProducts = Product::whereIn('id', $productIds)->get();
        $categoryIds = $relatedProducts->pluck('category_id')->filter()->unique()->toArray();
        $vendorIds = $relatedProducts->pluck('vendor_id')->filter()->unique()->toArray();

        $recommendations = Product::where('is_active', true)
            ->where(function ($query) use ($categoryIds, $vendorIds) {
                $query->whereIn('category_id', $categoryIds)
                    ->orWhereIn('vendor_id', $vendorIds);
            })
            ->whereNotIn('id', $productIds)
            ->inRandomOrder()
            ->take(4)
            ->get();

        if ($recommendations->count() < 4) {
            $additionalProducts = Product::where('is_active', true)
                ->whereNotIn('id', $productIds)
                ->whereNotIn('id', $recommendations->pluck('id')->toArray())
                ->inRandomOrder()
                ->take(4 - $recommendations->count())
                ->get();

            $recommendations = $recommendations->merge($additionalProducts);
        }

        $this->recommendedProducts = $recommendations;
    }

    public function render()
    {
        return view('livewire.cart.index');
    }
}
