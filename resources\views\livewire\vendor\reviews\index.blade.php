<div>
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Product Reviews</h1>
    </div>

    <div class="bg-white p-8 rounded-lg shadow-md">
        @if ($reviews->isEmpty())
            <div class="text-center py-12">
                <p class="text-gray-500 text-lg">You don't have any reviews yet.</p>
            </div>
        @else
            <div class="space-y-6">
                @foreach ($reviews as $review)
                    <div class="border-b border-gray-200 pb-6">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <img class="h-16 w-16 object-cover rounded-lg" src="{{ $review->product->image_url }}" alt="{{ $review->product->name }}">
                            </div>
                            <div class="flex-grow">
                                <h3 class="text-lg font-semibold text-gray-900">{{ $review->product->name }}</h3>
                                <div class="flex items-center mt-1">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <svg class="h-5 w-5 {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.96a1 1 0 00.95.69h4.162c.969 0 1.371 1.24.588 1.81l-3.368 2.448a1 1 0 00-.364 1.118l1.287 3.96c.3.921-.755 1.688-1.54 1.118l-3.368-2.448a1 1 0 00-1.176 0l-3.368 2.448c-.784.57-1.838-.197-1.539-1.118l1.286-3.96a1 1 0 00-.364-1.118L2.05 9.387c-.783-.57-.38-1.81.588-1.81h4.162a1 1 0 00.95-.69l1.286-3.96z" />
                                        </svg>
                                    @endfor
                                    <span class="ml-2 text-sm text-gray-600">by {{ $review->user->name }} on {{ $review->created_at->format('M d, Y') }}</span>
                                </div>
                                <p class="mt-3 text-gray-700">{{ $review->comment }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="mt-8">
                {{ $reviews->links() }}
            </div>
        @endif
    </div>
</div>
