<?php

namespace App\Livewire;

use App\Mail\ContactFormMail;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use WireUi\Traits\WireUiActions;

class ContactForm extends Component
{
    use WireUiActions;

    public $name = '';
    public $email = '';
    public $subject = '';
    public $message = '';

    protected function rules()
    {
        return [
            'name' => 'required|string|min:3',
            'email' => 'required|email',
            'subject' => 'required|string|min:5',
            'message' => 'required|string|min:10',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function submit(): void
    {
        $validatedData = $this->validate();

        try {
            Mail::to(config('mail.from.address'))
                ->send(new ContactFormMail($this->name, $this->email, $this->subject, $this->message));

            $this->notification()->success(
                'Message Sent!',
                'Thank you for contacting us. We will get back to you shortly.'
            );

            $this->reset();

        } catch (\Exception $e) {
            $this->notification()->error(
                'An error occurred',
                'Sorry, we could not send your message at this time. Please try again later.'
            );
        }
    }

    public function render()
    {
        return view('livewire.contact-form');
    }
}
