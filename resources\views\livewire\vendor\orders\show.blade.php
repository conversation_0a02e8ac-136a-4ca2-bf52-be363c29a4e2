<div>
    <div class="mb-4">
        <a href="{{ route('vendor.orders.index') }}" class="text-gray-700 hover:text-gray-900 font-medium">
            <i class="fas fa-arrow-left mr-2"></i> Back to Orders
        </a>
    </div>

    @if (session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('message') }}</span>
        </div>
    @endif

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-800">Order #{{ $order->id }}</h2>
            <span class="px-3 py-1 text-sm font-semibold rounded-full text-white 
                {{ $order->status == 'completed' ? 'bg-green-500' : '' }}
                {{ $order->status == 'processing' ? 'bg-yellow-500' : '' }}
                {{ $order->status == 'shipping' ? 'bg-blue-500' : '' }}
                {{ $order->status == 'cancelled' ? 'bg-red-500' : '' }}
                {{ !in_array($order->status, ['completed', 'processing', 'shipping', 'cancelled']) ? 'bg-gray-500' : '' }}
            ">
                {{ ucfirst($order->status) }}
            </span>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Order Information</h3>
                    <p><strong>Order Date:</strong> {{ $order->created_at->format('M d, Y H:i') }}</p>
                    <p><strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}</p>
                    <p><strong>Payment Status:</strong> 
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $order->payment_status == 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ ucfirst($order->payment_status) }}
                        </span>
                    </p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Customer Information</h3>
                    <p><strong>Name:</strong> {{ $order->user->name ?? 'Guest' }}</p>
                    <p><strong>Email:</strong> {{ $order->user->email ?? $order->email }}</p>
                    <p><strong>Phone:</strong> {{ $order->phone ?? 'N/A' }}</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Shipping Address</h3>
                    <address class="not-italic text-gray-700">
                        {{ $order->shipping_address ?? 'N/A' }}<br>
                        {{ $order->shipping_city ?? '' }}, {{ $order->shipping_state ?? '' }} {{ $order->shipping_zip ?? '' }}<br>
                        {{ $order->shipping_country ?? '' }}
                    </address>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Billing Address</h3>
                     <address class="not-italic text-gray-700">
                        {{ $order->billing_address ?? 'Same as shipping' }}<br>
                        {{ $order->billing_city ?? '' }}, {{ $order->billing_state ?? '' }} {{ $order->billing_zip ?? '' }}<br>
                        {{ $order->billing_country ?? '' }}
                    </address>
                </div>
            </div>

            <div>
                <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Order Items</h3>
                <div class="-mx-4 sm:-mx-8 px-4 sm:px-8 py-4 overflow-x-auto">
                    <div class="inline-block min-w-full shadow rounded-lg overflow-hidden">
                        <table class="min-w-full leading-normal">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Product</th>
                                    <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Price</th>
                                    <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Quantity</th>
                                    <th class="px-5 py-3 border-b-2 border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($vendorItems as $item)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 w-12 h-12">
                                                    <img class="w-full h-full rounded-md object-cover" src="{{ $item->product->image_url ?? asset('images/default-product.png') }}" alt="{{ $item->product_name }}" />
                                                </div>
                                                <div class="ml-4">
                                                    <p class="text-gray-900 whitespace-no-wrap font-semibold">{{ $item->product_name }}</p>
                                                    <p class="text-gray-600 whitespace-no-wrap text-xs">SKU: {{ $item->product->id ?? 'N/A' }}</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">${{ number_format($item->price, 2) }}</td>
                                        <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ $item->quantity }}</td>
                                        <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-right">${{ number_format($item->price * $item->quantity, 2) }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center py-10 text-gray-500">No items found for your store in this order.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                            <tfoot class="bg-gray-50 font-semibold">
                                <tr>
                                    <td colspan="3" class="px-5 py-3 border-b-2 border-gray-200 text-right">Subtotal</td>
                                    <td class="px-5 py-3 border-b-2 border-gray-200 text-right">${{ number_format($subtotal, 2) }}</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-5 py-3 border-b-2 border-gray-200 text-right">Commission (10%)</td>
                                    <td class="px-5 py-3 border-b-2 border-gray-200 text-right">-${{ number_format($commission, 2) }}</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-5 py-3 border-b-2 border-gray-200 text-right text-lg">Your Earnings</td>
                                    <td class="px-5 py-3 border-b-2 border-gray-200 text-right text-lg">${{ number_format($netAmount, 2) }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <div class="mt-6">
                <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Update Order Status</h3>
                <form wire:submit.prevent="updateStatus" class="flex items-center">
                    <select wire:model.defer="newStatus" class="block w-full max-w-xs rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm">
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipping">Shipping</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">
                        Update Status
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
