<?php

namespace App\Livewire\Product;

use App\Models\Product;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Url;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Search extends Component
{
    use WithPagination;

    #[Url(as: 'q', except: '')]
    public $query = '';

    public function render()
    {
        $products = Product::query()
            ->where('is_active', true)
            ->where(function ($q) {
                $q->where('name', 'like', '%' . $this->query . '%')
                  ->orWhere('description', 'like', '%' . $this->query . '%');
            })
            ->with(['vendor', 'category', 'brand'])
            ->latest()
            ->paginate(12);

        return view('livewire.product.search', [
            'products' => $products,
        ]);
    }
}
