<?php

namespace App\Livewire\Product;

use App\Models\Category;
use App\Models\Product;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class CategoryProducts extends Component
{
    use WithPagination;

    public Category $category;
    public $search = '';
    public $sortBy = 'latest';
    public $priceRange = 'all';

    protected $queryString = [
        'search' => ['except' => ''],
        'sortBy' => ['except' => 'latest'],
        'priceRange' => ['except' => 'all'],
    ];

    public function mount(Category $category)
    {
        $this->category = $category;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSortBy()
    {
        $this->resetPage();
    }

    public function updatingPriceRange()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Product::where('category_id', $this->category->id)
            ->where('is_active', true)
            ->with(['vendor', 'category']);

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }

        // Apply price range filter
        if ($this->priceRange !== 'all') {
            switch ($this->priceRange) {
                case 'under-1000':
                    $query->where('price', '<', 1000);
                    break;
                case '1000-5000':
                    $query->whereBetween('price', [1000, 5000]);
                    break;
                case '5000-10000':
                    $query->whereBetween('price', [5000, 10000]);
                    break;
                case 'over-10000':
                    $query->where('price', '>', 10000);
                    break;
            }
        }

        // Apply sorting
        switch ($this->sortBy) {
            case 'price-low':
                $query->orderBy('price', 'asc');
                break;
            case 'price-high':
                $query->orderBy('price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'latest':
            default:
                $query->latest();
                break;
        }

        $products = $query->paginate(12);

        // Get subcategories
        $subcategories = $this->category->children()->get();

        return view('livewire.product.category-products', [
            'products' => $products,
            'subcategories' => $subcategories,
        ]);
    }
}
