<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadService
{
    /**
     * Upload and process an image file
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param array $options
     * @return string
     */
    public function uploadImage(UploadedFile $file, string $directory, array $options = []): string
    {
        $options = array_merge([
            'disk' => 'public',
            'resize' => null, // ['width' => 800, 'height' => 600]
            'quality' => 85,
            'format' => 'webp', // Convert to WebP for better compression
            'preserve_original' => false,
        ], $options);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file, $options['format']);
        $path = $directory . '/' . $filename;

        // Process image if resize options are provided
        if ($options['resize'] || $options['format'] !== $file->getClientOriginalExtension()) {
            $processedImage = $this->processImage($file, $options);
            Storage::disk($options['disk'])->put($path, $processedImage);
        } else {
            // Store original file
            $path = $file->store($directory, $options['disk']);
        }

        return $path;
    }

    /**
     * Delete a file from storage
     *
     * @param string|null $path
     * @param string $disk
     * @return bool
     */
    public function deleteFile(?string $path, string $disk = 'public'): bool
    {
        if (!$path) {
            return false;
        }

        // Handle full URLs by extracting the path
        if (Str::startsWith($path, ['http://', 'https://'])) {
            $path = $this->extractPathFromUrl($path, $disk);
        }

        if (Storage::disk($disk)->exists($path)) {
            return Storage::disk($disk)->delete($path);
        }

        return false;
    }

    /**
     * Get the public URL for a file
     *
     * @param string|null $path
     * @param string $disk
     * @param string|null $default
     * @return string
     */
    public function getFileUrl(?string $path, string $disk = 'public', ?string $default = null): string
    {
        if (!$path) {
            return $default ?? asset('images/placeholder.png');
        }

        // If it's already a full URL, return it
        if (Str::startsWith($path, ['http://', 'https://'])) {
            return $path;
        }

        // Check if file exists
        if (Storage::disk($disk)->exists($path)) {
            return Storage::disk($disk)->url($path);
        }

        return $default ?? asset('images/placeholder.png');
    }

    /**
     * Process image with resize and format conversion
     *
     * @param UploadedFile $file
     * @param array $options
     * @return string
     */
    private function processImage(UploadedFile $file, array $options): string
    {
        $image = Image::make($file->getRealPath());

        // Resize if dimensions are provided
        if ($options['resize']) {
            $width = $options['resize']['width'] ?? null;
            $height = $options['resize']['height'] ?? null;
            
            if ($width && $height) {
                $image->fit($width, $height, function ($constraint) {
                    $constraint->upsize();
                });
            } elseif ($width) {
                $image->widen($width, function ($constraint) {
                    $constraint->upsize();
                });
            } elseif ($height) {
                $image->heighten($height, function ($constraint) {
                    $constraint->upsize();
                });
            }
        }

        // Convert format and set quality
        $format = $options['format'];
        $quality = $options['quality'];

        switch ($format) {
            case 'webp':
                return $image->encode('webp', $quality)->__toString();
            case 'jpg':
            case 'jpeg':
                return $image->encode('jpg', $quality)->__toString();
            case 'png':
                return $image->encode('png')->__toString();
            default:
                return $image->encode($format, $quality)->__toString();
        }
    }

    /**
     * Generate a unique filename
     *
     * @param UploadedFile $file
     * @param string $format
     * @return string
     */
    private function generateUniqueFilename(UploadedFile $file, string $format): string
    {
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $sanitizedName = Str::slug($originalName);
        $timestamp = now()->format('Y-m-d-H-i-s');
        $random = Str::random(8);
        
        return "{$sanitizedName}-{$timestamp}-{$random}.{$format}";
    }

    /**
     * Extract file path from full URL
     *
     * @param string $url
     * @param string $disk
     * @return string
     */
    private function extractPathFromUrl(string $url, string $disk): string
    {
        $diskUrl = Storage::disk($disk)->url('');
        return Str::after($url, $diskUrl);
    }

    /**
     * Migrate file from one disk to another
     *
     * @param string $path
     * @param string $fromDisk
     * @param string $toDisk
     * @param string|null $newDirectory
     * @return string|null
     */
    public function migrateFile(string $path, string $fromDisk, string $toDisk, ?string $newDirectory = null): ?string
    {
        if (!Storage::disk($fromDisk)->exists($path)) {
            return null;
        }

        $fileContent = Storage::disk($fromDisk)->get($path);
        $filename = basename($path);
        
        if ($newDirectory) {
            $newPath = $newDirectory . '/' . $filename;
        } else {
            $newPath = $path;
        }

        if (Storage::disk($toDisk)->put($newPath, $fileContent)) {
            return $newPath;
        }

        return null;
    }

    /**
     * Get file size in human readable format
     *
     * @param string $path
     * @param string $disk
     * @return string
     */
    public function getFileSize(string $path, string $disk = 'public'): string
    {
        if (!Storage::disk($disk)->exists($path)) {
            return '0 B';
        }

        $bytes = Storage::disk($disk)->size($path);
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
