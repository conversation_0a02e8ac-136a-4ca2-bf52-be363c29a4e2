<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        SubscriptionPlan::updateOrCreate(
            ['name' => 'Free Plan'],
            [
                'price' => 0,
                'interval' => 'monthly',
                'duration_days' => 30,
                'order_limit' => 10,
                'features' => json_encode([
                    '10 orders per month',
                    'Basic support',
                ]),
                'status' => 'active',
            ]
        );

        SubscriptionPlan::updateOrCreate(
            ['name' => 'Monthly Plan'],
            [
                'price' => 1000000, // 10,000 NGN in kobo
                'interval' => 'monthly',
                'duration_days' => 30,
                'order_limit' => null, // Unlimited
                'features' => json_encode([
                    'Unlimited orders',
                    'Priority support',
                    'Advanced analytics',
                ]),
                'paystack_plan_code' => 'PLN_REPLACE_WITH_ACTUAL_CODE', // Replace with actual Paystack plan code
                'status' => 'active',
            ]
        );

        SubscriptionPlan::updateOrCreate(
            ['name' => '6-Month Plan'],
            [
                'price' => 950000, // 9,500 NGN in kobo, per month
                'interval' => 'monthly',
                'duration_days' => 180,
                'order_limit' => null, // Unlimited
                'features' => json_encode([
                    'Unlimited orders',
                    'Priority support',
                    'Advanced analytics',
                    '5% discount',
                ]),
                'paystack_plan_code' => 'PLN_REPLACE_WITH_ACTUAL_CODE', // Replace with actual Paystack plan code
                'status' => 'active',
            ]
        );
        
        SubscriptionPlan::updateOrCreate(
            ['name' => '12-Month Plan'],
            [
                'price' => 900000, // 9,000 NGN in kobo, per month
                'interval' => 'annually',
                'duration_days' => 365,
                'order_limit' => null, // Unlimited
                'features' => json_encode([
                    'Unlimited orders',
                    'Priority support',
                    'Advanced analytics',
                    '10% discount',
                ]),
                'paystack_plan_code' => 'PLN_REPLACE_WITH_ACTUAL_CODE', // Replace with actual Paystack plan code
                'status' => 'active',
            ]
        );
    }
}
