<?php

namespace App\Livewire\Vendor\Orders;

use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.vendor')]
class Show extends Component
{
    public Order $order;
    public $vendorItems;
    public $subtotal = 0;
    public $commission = 0;
    public $netAmount = 0;
    public $newStatus;

    public function mount(Order $order)
    {
        $this->order = $order;
        $this->newStatus = $this->order->status;
        $this->calculateTotals();
    }

    public function calculateTotals()
    {
        $vendor = Auth::user()->vendor;
        $this->vendorItems = $this->order->items->filter(function ($item) use ($vendor) {
            return $item->product && $item->product->vendor_id == $vendor->id;
        });

        $this->subtotal = $this->vendorItems->sum(function ($item) {
            return $item->price * $item->quantity;
        });

        // Assuming a 10% commission rate as per the old view
        $this->commission = $this->subtotal * 0.10;
        $this->netAmount = $this->subtotal - $this->commission;
    }

    public function updateStatus()
    {
        $this->validate(['newStatus' => 'required|in:pending,processing,shipping,completed,cancelled']);

        $this->order->update(['status' => $this->newStatus]);

        session()->flash('message', 'Order status successfully updated.');

        // Refresh component state
        $this->order = $this->order->fresh();
    }

    public function render()
    {
        return view('livewire.vendor.orders.show');
    }
}
