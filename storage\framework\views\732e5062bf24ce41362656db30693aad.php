<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Earnings & Withdrawals
                </h1>
                <p class="text-gray-300 text-lg">Manage your earnings and request withdrawals</p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('vendor.dashboard')); ?>"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    
    <!--[if BLOCK]><![endif]--><?php if(session()->has('success')): ?>
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 text-green-700 p-6 mb-8 rounded-r-2xl shadow-lg" role="alert">
            <div class="flex items-center">
                <div class="p-2 bg-green-500 rounded-full mr-4">
                    <i class="fas fa-check text-white"></i>
                </div>
                <p class="font-semibold"><?php echo e(session('success')); ?></p>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php if(session()->has('error')): ?>
        <div class="bg-gradient-to-r from-red-50 to-pink-50 border-l-4 border-red-500 text-red-700 p-6 mb-8 rounded-r-2xl shadow-lg" role="alert">
            <div class="flex items-center">
                <div class="p-2 bg-red-500 rounded-full mr-4">
                    <i class="fas fa-exclamation-triangle text-white"></i>
                </div>
                <p class="font-semibold"><?php echo e(session('error')); ?></p>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-wallet text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-blue-100 text-blue-800 transition-all duration-300">
                        Total
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Balance</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    <?php echo '₦' . number_format($balance, 2); ?>
                </p>
                <p class="text-xs text-gray-400">from all transactions</p>
            </div>
        </div>

        
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-money-bill-wave text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-green-100 text-green-800 transition-all duration-300">
                        Available
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Withdrawable Balance</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">
                    <?php echo '₦' . number_format($withdrawableBalance, 2); ?>
                </p>
                <div class="mt-4">
                    <button x-data @click="$dispatch('open-modal')"
                            class="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold py-3 px-4 rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 hover:scale-105 shadow-lg">
                        <i class="fas fa-download mr-2"></i>
                        Withdraw Funds
                    </button>
                </div>
            </div>
        </div>

        
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-arrow-down text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-purple-100 text-purple-800 transition-all duration-300">
                        Withdrawn
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Withdrawn</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">
                    <?php echo '₦' . number_format($totalWithdrawn, 2); ?>
                </p>
                <p class="text-xs text-gray-400">successfully withdrawn</p>
            </div>
        </div>

        
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chart-line text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 transition-all duration-300">
                        Lifetime
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Earnings</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">
                    <?php echo '₦' . number_format($totalEarnings, 2); ?>
                </p>
                <p class="text-xs text-gray-400">lifetime earnings</p>
            </div>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b">
            <h5 class="font-bold text-lg">Transaction History</h5>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full leading-normal">
                <thead>
                    <tr class="border-b bg-gray-50">
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                        <th class="px-5 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
                        <th class="px-5 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Balance</th>
                    </tr>
                </thead>
                <tbody>
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-5 py-5 border-b border-gray-200 text-sm"><?php echo e($transaction->created_at->format('M d, Y H:i')); ?></td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($transaction->amount > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?> text-capitalize">
                                    <?php echo e($transaction->type); ?>

                                </span>
                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm"><?php echo e($transaction->description); ?></td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm text-right font-semibold <?php echo e($transaction->amount < 0 ? 'text-red-600' : 'text-green-600'); ?>">
                                ₦<?php echo e(number_format(abs($transaction->amount), 2)); ?>

                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm text-right">₦<?php echo e(number_format($transaction->balance_after, 2)); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center py-10 text-gray-500">No transactions found.</td>
                        </tr>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4">
            <?php echo e($transactions->links()); ?>

        </div>
    </div>

    
    <div x-data="{ show: false }"
         @open-modal.window="show = true"
         @close-modal.window="show = false"
         x-show="show"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
         style="display: none;">
        <div x-show="show"
             x-transition:enter="transition ease-out duration-300 transform"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="transition ease-in duration-200 transform"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             class="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-auto overflow-hidden"
             @click.away="show = false">

            
            <div class="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="p-2 bg-white bg-opacity-20 rounded-lg">
                            <i class="fas fa-money-bill-wave text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Request Withdrawal</h3>
                            <p class="text-green-100 text-sm">Bank transfer to your account</p>
                        </div>
                    </div>
                    <button @click="show = false" class="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-all duration-300">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
            </div>

            
            <div class="p-6">
                <form wire:submit.prevent="withdraw" class="space-y-6">
                    
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 p-4 rounded-r-xl">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-500 rounded-full mr-3">
                                <i class="fas fa-info text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="text-blue-800 text-sm font-medium">Withdrawal Information</p>
                                <p class="text-blue-700 text-xs mt-1">Processed via bank transfer to Nigerian accounts. Minimum: ₦1,000</p>
                            </div>
                        </div>
                    </div>

                    
                    <div>
                        <label for="amount" class="block text-sm font-bold text-gray-900 mb-2">
                            <i class="fas fa-naira-sign mr-2 text-gray-500"></i>Withdrawal Amount
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-lg">₦</span>
                            </div>
                            <input type="number"
                                   wire:model.live.debounce.300ms="amount"
                                   id="amount"
                                   class="block w-full pl-8 pr-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-green-500 focus:ring-0 transition-all duration-300 <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   step="100"
                                   min="1000"
                                   placeholder="Enter amount to withdraw"
                                   required>
                        </div>
                        <div class="flex items-center justify-between mt-2">
                            <p class="text-xs text-gray-500">Available: <?php echo '₦' . number_format($withdrawableBalance, 2); ?></p>
                            <button type="button"
                                    wire:click="$set('amount', <?php echo e($withdrawableBalance); ?>)"
                                    class="text-xs text-green-600 hover:text-green-800 font-medium transition-colors duration-300">
                                Withdraw All
                            </button>
                        </div>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                            </p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                    
                    <div>
                        <label for="bank_code" class="block text-sm font-bold text-gray-900 mb-2">
                            <i class="fas fa-university mr-2 text-gray-500"></i>Bank Name
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-building text-gray-400"></i>
                            </div>
                            <select wire:model.live="bank_code"
                                    id="bank_code"
                                    class="block w-full pl-10 pr-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 focus:border-green-500 focus:ring-0 transition-all duration-300 appearance-none bg-white <?php $__errorArgs = ['bank_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    required>
                                <option value="">Select your bank</option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $banks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bank): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($bank['code']); ?>"><?php echo e($bank['name']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['bank_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                            </p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    
                    <div>
                        <label for="account_name" class="block text-sm font-bold text-gray-900 mb-2">
                            <i class="fas fa-user mr-2 text-gray-500"></i>Account Name
                            <!--[if BLOCK]><![endif]--><?php if($account_verified): ?>
                                <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Verified
                                </span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-id-card text-gray-400"></i>
                            </div>
                            <!--[if BLOCK]><![endif]--><?php if($verifying_account): ?>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <input type="text"
                                   wire:model.live.debounce.300ms="account_name"
                                   id="account_name"
                                   class="block w-full pl-10 pr-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-green-500 focus:ring-0 transition-all duration-300 <?php if($account_verified): ?> border-green-500 bg-green-50 <?php elseif($errors->has('account_name')): ?> border-red-500 focus:border-red-500 <?php endif; ?>"
                                   placeholder="Account name will be auto-filled"
                                   readonly="<?php echo e($account_verified ? 'readonly' : ''); ?>"
                                   required>
                        </div>
                        <!--[if BLOCK]><![endif]--><?php if($account_verified): ?>
                            <p class="mt-2 text-sm text-green-600 flex items-center">
                                <i class="fas fa-check-circle mr-1"></i>Account verified successfully
                            </p>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['account_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                            </p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    
                    <div>
                        <label for="account_number" class="block text-sm font-bold text-gray-900 mb-2">
                            <i class="fas fa-hashtag mr-2 text-gray-500"></i>Account Number
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-credit-card text-gray-400"></i>
                            </div>
                            <input type="text"
                                   wire:model.live.debounce.300ms="account_number"
                                   id="account_number"
                                   class="block w-full pl-10 pr-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-green-500 focus:ring-0 transition-all duration-300 <?php $__errorArgs = ['account_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   maxlength="10"
                                   pattern="[0-9]{10}"
                                   placeholder="Enter 10-digit account number"
                                   required>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Enter your 10-digit account number</p>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['account_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                            </p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                    </div>

                    
                    <div class="flex items-center justify-between pt-6 border-t border-gray-100">
                        <div class="text-sm text-gray-500">
                            <i class="fas fa-shield-alt mr-1"></i>
                            Secure bank transfer processing
                        </div>
                        <div class="flex items-center space-x-3">
                            <button type="button"
                                    @click="show = false"
                                    class="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-300">
                                Cancel
                            </button>
                            <button type="submit"
                                    wire:loading.attr="disabled"
                                    wire:target="withdraw"
                                    class="group bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:from-green-600 hover:to-emerald-700 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2">
                                <div wire:loading wire:target="withdraw" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                <i class="fas fa-paper-plane transition-transform duration-300 group-hover:translate-x-1" wire:loading.remove wire:target="withdraw"></i>
                                <span wire:loading.remove wire:target="withdraw">Request Withdrawal</span>
                                <span wire:loading wire:target="withdraw">Processing...</span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    
    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('account-verified', (event) => {
                // Show success notification
                if (typeof window.showNotification === 'function') {
                    window.showNotification('success', event.message);
                } else {
                    alert('✅ ' + event.message);
                }
            });

            Livewire.on('account-verification-failed', (event) => {
                // Show error notification
                if (typeof window.showNotification === 'function') {
                    window.showNotification('error', event.message);
                } else {
                    alert('❌ ' + event.message);
                }
            });
        });
    </script>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/vendor/earnings/index.blade.php ENDPATH**/ ?>