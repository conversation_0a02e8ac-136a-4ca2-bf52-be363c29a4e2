<?php

namespace App\Livewire\Admin\Payments;

use App\Models\Payment;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public function getStatusClass($status)
    {
        return match ($status) {
            'successful' => 'bg-green-100 text-green-800',
            'failed' => 'bg-red-100 text-red-800',
            'pending' => 'bg-yellow-100 text-yellow-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    public function render()
    {
        $payments = Payment::with('user')->latest()->paginate(15);
        return view('livewire.admin.payments.index', [
            'payments' => $payments,
        ]);
    }
}
