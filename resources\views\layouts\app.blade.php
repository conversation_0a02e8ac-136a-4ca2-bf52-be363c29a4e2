<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? config('app.name', 'Brandify') }}</title>

    <!-- Vite Assets -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Animate.css for UI effects -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>


    @livewireStyles
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />
    @stack('styles')


</head>
<body class="font-sans antialiased text-gray-900 bg-white">

<!-- Navigation -->
<header x-data="{ mobileMenuOpen: false, userMenuOpen: false }" class="fixed top-0 right-0 left-0 z-50 bg-white shadow-sm">
    <nav class="container px-4 mx-auto sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="{{ route('home') }}" class="flex items-center space-x-2">
                    <img src="{{ asset('storage/brandify.png') }}" alt="Brandify Logo" class="w-auto h-8" onerror="this.onerror=null; this.src='https://via.placeholder.com/80x32?text=Brandify';">
                    <span class="hidden text-xl font-bold text-gray-800 sm:inline">{{ config('app.name', 'Brandify') }}</span>
                </a>
            </div>

            <!-- Desktop Menu -->
            <div class="hidden lg:flex lg:items-center lg:space-x-8">
                <a href="{{ route('home') }}" class="font-medium text-gray-700 hover:text-black">Home</a>
                <a href="{{ route('products.index') }}" class="font-medium text-gray-700 hover:text-black">Shop</a>
                <a href="{{ route('about') }}" class="font-medium text-gray-700 hover:text-black">About</a>
                <a href="{{ route('contact') }}" class="font-medium text-gray-700 hover:text-black">Contact</a>
            </div>

            <!-- Right side icons & search -->
            <div class="flex items-center space-x-4">
                <!-- Livewire Search Component -->
                @livewire('search')

                <!-- Icons -->
                <div class="hidden items-center space-x-4 lg:flex">
                    @auth
                        <!-- User Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-600 hover:text-black focus:outline-none">
                                <i class="text-xl fa-regular fa-user"></i>
                            </button>
                            <div x-show="open" @click.away="open = false" class="absolute right-0 z-50 py-1 mt-2 w-48 bg-white rounded-md shadow-lg"
                                x-transition:enter="transition ease-out duration-200"
                                x-transition:enter-start="transform opacity-0 scale-95"
                                x-transition:enter-end="transform opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-75"
                                x-transition:leave-start="transform opacity-100 scale-100"
                                x-transition:leave-end="transform opacity-0 scale-95"
                                style="display: none;">
                                <span class="block px-4 py-2 text-sm text-gray-700">{{ auth()->user()->name }}</span>
                                <div class="border-t border-gray-100"></div>
                                @if(optional(auth()->user())->isAdmin())
                                    <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Admin Dashboard</a>
                                @endif
                                @if(optional(auth()->user())->isVendor())
                                    <a href="{{ route('vendor.dashboard') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Vendor Dashboard</a>
                                @endif
                                <a href="{{ route('wishlist.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Wishlist</a>
                                <a href="{{ route('orders.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Orders</a>
                                <div class="border-t border-gray-100"></div>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="block px-4 py-2 w-full text-sm text-left text-gray-700 hover:bg-gray-100">Logout</button>
                                </form>
                            </div>
                        </div>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-600 hover:text-black" title="Login">
                            <i class="text-xl fa-regular fa-user"></i>
                        </a>
                    @endauth

                    <a href="{{ route('wishlist.index') }}" class="text-gray-600 hover:text-black" title="Wishlist">
                        <i class="text-xl fa-regular fa-heart"></i>
                    </a>

                    @livewire('cart-counter')
                </div>

                <!-- Mobile Menu Button -->
                <div class="flex lg:hidden">
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="inline-flex justify-center items-center p-2 text-gray-600 rounded-md hover:text-black hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-black">
                        <span class="sr-only">Open main menu</span>
                        <i class="text-xl fa-solid fa-bars" x-show="!mobileMenuOpen"></i>
                        <i class="text-xl fa-solid fa-xmark" x-show="mobileMenuOpen"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen" class="lg:hidden"
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 -translate-y-1"
            x-transition:enter-end="opacity-100 translate-y-0"
            x-transition:leave="transition ease-in duration-150"
            x-transition:leave-start="opacity-100 translate-y-0"
            x-transition:leave-end="opacity-0 -translate-y-1"
            style="display: none;">
            <div class="pt-2 pb-3 space-y-1">
                <a href="{{ route('home') }}" class="block py-2 pr-4 pl-3 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800">Home</a>
                <a href="{{ route('products.index') }}" class="block py-2 pr-4 pl-3 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800">Shop</a>
                <a href="{{ route('about') }}" class="block py-2 pr-4 pl-3 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800">About</a>
                <a href="{{ route('contact') }}" class="block py-2 pr-4 pl-3 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800">Contact</a>
            </div>
            <div class="pt-4 pb-3 border-t border-gray-200">
                @auth
                    <div class="flex items-center px-4">
                        <div class="ml-3">
                            <div class="text-base font-medium text-gray-800">{{ optional(auth()->user())->name }}</div>
                            <div class="text-sm font-medium text-gray-500">{{ optional(auth()->user())->email }}</div>
                        </div>
                    </div>
                    <div class="mt-3 space-y-1">
                        @if(optional(auth()->user())->isAdmin())
                            <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">Admin Dashboard</a>
                        @endif
                        @if(optional(auth()->user())->isVendor())
                            <a href="{{ route('vendor.dashboard') }}" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">Vendor Dashboard</a>
                        @endif
                        <a href="{{ route('orders.index') }}" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">My Orders</a>
                        <a href="{{ route('wishlist.index') }}" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">My Wishlist</a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="block px-4 py-2 w-full text-base font-medium text-left text-gray-500 hover:text-gray-800 hover:bg-gray-100">Logout</button>
                        </form>
                    </div>
                @else
                    <div class="space-y-1">
                        <a href="{{ route('login') }}" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">Login</a>
                        <a href="{{ route('register') }}" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">Register</a>
                    </div>
                @endauth
            </div>
        </div>
    </nav>
</header>

    <!-- Main Content -->
    <main class="container px-4 pt-24 mx-auto sm:px-6 lg:px-8">
        <!-- Flash Messages -->








        @if (isset($slot))
            {{ $slot }}
        @else
            @yield('content')
        @endif
    </main>


    @livewireScripts
    
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const heroSlider = new Swiper('.hero-slider', {
                // Optional parameters
                loop: true,
                effect: 'fade',
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },

                // If we need pagination
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },

                // Navigation arrows
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });
        });
    </script>
    @stack('scripts')

    @include('layouts._footer')

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('a[href="#"]').forEach(function (anchor) {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    alert('This feature is currently under development and will be available soon.');
                });
            });
        });
    </script>
</body>
</html>
