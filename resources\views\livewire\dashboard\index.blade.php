<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Welcome Back, {{ Auth::user()->name }}
                </h1>
                <p class="text-gray-300 text-lg">Track your orders and discover amazing products</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('products.index') }}"
                   class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center space-x-2">
                    <i class="fa-solid fa-shopping-cart transition-transform duration-300 group-hover:bounce"></i>
                    <span>Continue Shopping</span>
                </a>
                <a href="{{ route('wishlist.index') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-heart transition-transform duration-300 group-hover:scale-110"></i>
                    <span>My Wishlist</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Modern Stats Grid --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {{-- Total Orders Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-shopping-bag text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-blue-100 text-blue-800 transition-all duration-300">
                        All Time
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Orders</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    {{ number_format($totalOrders) }}
                </p>
                <p class="text-xs text-gray-400">orders placed</p>
            </div>
        </div>

        {{-- Additional stats cards --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-heart text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-pink-100 text-pink-800 transition-all duration-300">
                        Saved
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Wishlist Items</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-pink-600 transition-colors duration-300">
                    {{ number_format($wishlistItems) }}
                </p>
                <p class="text-xs text-gray-400">items saved</p>
            </div>
        </div>

        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-money-bill-wave text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-green-100 text-green-800 transition-all duration-300">
                        Total
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Spent</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">
                    @currency(Auth::user()->orders()->sum('total'))
                </p>
                <p class="text-xs text-gray-400">lifetime spending</p>
            </div>
        </div>

        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-user-circle text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-green-100 text-green-800 transition-all duration-300">
                        Active
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Member Since</p>
                <p class="text-2xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">
                    {{ Auth::user()->created_at->format('M Y') }}
                </p>
                <p class="text-xs text-gray-400">{{ Auth::user()->created_at->diffForHumans() }}</p>
            </div>
        </div>
    </div>

    {{-- Main Content Grid --}}
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {{-- Left Column - Orders and Activity --}}
        <div class="lg:col-span-2 space-y-8">
            {{-- Modern Recent Orders --}}
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
                <div class="p-8 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Recent Orders</h3>
                            <p class="text-gray-500 mt-1">Your latest purchases</p>
                        </div>
                        <a href="{{ route('orders.index') }}"
                           class="group inline-flex items-center px-4 py-2 bg-black text-white rounded-xl font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                            <span>View All</span>
                            <i class="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                        </a>
                    </div>
                </div>
                <div class="overflow-hidden">
                    @forelse($recentOrders as $index => $order)
                        <div class="p-6 border-b border-gray-50 hover:bg-gray-50 transition-all duration-300 {{ $index === 0 ? 'bg-gradient-to-r from-blue-50 to-transparent' : '' }}">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white font-bold">
                                        #{{ $order->id }}
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900">Order #{{ $order->id }}</p>
                                        <p class="text-sm text-gray-500">{{ $order->created_at->format('M d, Y \a\t g:i A') }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="text-right">
                                        <p class="font-bold text-lg text-gray-900">@currency($order->total)</p>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {{ $order->status === 'completed' ? 'bg-green-100 text-green-800' : ($order->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                            {{ ucfirst($order->status) }}
                                        </span>
                                    </div>
                                    <a href="{{ route('orders.show', $order) }}"
                                       class="p-2 text-gray-400 hover:text-black hover:bg-gray-100 rounded-lg transition-all duration-300">
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-12 text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-shopping-bag text-gray-400 text-xl"></i>
                            </div>
                            <p class="text-gray-500 font-medium">No orders yet</p>
                            <p class="text-gray-400 text-sm mt-1">Start shopping to see your orders here</p>
                            <a href="{{ route('products.index') }}"
                               class="inline-flex items-center px-4 py-2 bg-black text-white rounded-xl font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105 mt-4">
                                <i class="fas fa-shopping-cart mr-2"></i>
                                <span>Start Shopping</span>
                            </a>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        {{-- Right Sidebar --}}
        <div class="lg:col-span-1 space-y-8">
            {{-- Recommended Products --}}
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">Recommended</h3>
                            <p class="text-gray-500 text-sm mt-1">Products you might like</p>
                        </div>
                        <a href="{{ route('products.index') }}"
                           class="text-black hover:text-gray-600 transition-colors duration-300 font-medium text-sm">
                            View All →
                        </a>
                    </div>
                </div>
                <div class="p-6 space-y-4">
                    @forelse($recommendedProducts as $product)
                        <div class="group flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-all duration-300">
                            <div class="relative">
                                <img src="{{ $product->image_url }}"
                                     alt="{{ $product->name }}"
                                     class="w-12 h-12 rounded-lg object-cover group-hover:scale-105 transition-transform duration-300">
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-300">
                                    {{ $product->name }}
                                </p>
                                <p class="text-sm text-gray-500">@currency($product->price)</p>
                            </div>
                            <a href="{{ route('products.show', $product) }}"
                               class="p-2 text-gray-400 hover:text-black hover:bg-gray-100 rounded-lg transition-all duration-300">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-star text-gray-400"></i>
                            </div>
                            <p class="text-gray-500 font-medium">No recommendations</p>
                            <p class="text-gray-400 text-sm">Check back later for suggestions</p>
                        </div>
                    @endforelse
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
                <div class="p-6 border-b border-gray-100">
                    <h3 class="text-xl font-bold text-gray-900">Quick Actions</h3>
                    <p class="text-gray-500 text-sm mt-1">Manage your account</p>
                </div>
                <div class="p-6 space-y-3">
                    <a href="{{ route('orders.index') }}"
                       class="group w-full flex items-center px-4 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl text-blue-700 hover:from-blue-100 hover:to-indigo-100 hover:border-blue-300 transition-all duration-300 hover:scale-105">
                        <div class="p-2 bg-blue-500 rounded-lg mr-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-shopping-bag text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-semibold">My Orders</p>
                            <p class="text-xs text-blue-600">Track your purchases</p>
                        </div>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>

                    <a href="{{ route('wishlist.index') }}"
                       class="group w-full flex items-center px-4 py-4 bg-gradient-to-r from-pink-50 to-red-50 border border-pink-200 rounded-xl text-pink-700 hover:from-pink-100 hover:to-red-100 hover:border-pink-300 transition-all duration-300 hover:scale-105">
                        <div class="p-2 bg-pink-500 rounded-lg mr-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-heart text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-semibold">My Wishlist</p>
                            <p class="text-xs text-pink-600">Saved items</p>
                        </div>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>

                    <a href="{{ route('profile.edit') }}"
                       class="group w-full flex items-center px-4 py-4 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl text-purple-700 hover:from-purple-100 hover:to-indigo-100 hover:border-purple-300 transition-all duration-300 hover:scale-105">
                        <div class="p-2 bg-purple-500 rounded-lg mr-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-semibold">Profile Settings</p>
                            <p class="text-xs text-purple-600">Update your info</p>
                        </div>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
