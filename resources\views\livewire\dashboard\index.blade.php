<div>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <x-card title="Total Orders">
                    <div class="text-3xl font-bold">{{ $totalOrders }}</div>
                </x-card>
                <x-card title="Items in Wishlist">
                    <div class="text-3xl font-bold">{{ $wishlistItems }}</div>
                </x-card>
            </div>

            <!-- Recent Orders -->
            <div class="mt-8">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Recent Orders</h3>
                <div class="mt-4 bg-white dark:bg-gray-800 shadow-sm sm:rounded-lg overflow-hidden">
                    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse ($recentOrders as $order)
                            <li class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition ease-in-out duration-150">
                                <a href="#" class="block">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-indigo-600 truncate">
                                            Order #{{ $order->id }}
                                        </p>
                                        <div class="ml-2 flex-shrink-0 flex">
                                            <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                {{ $order->status }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="mt-2 sm:flex sm:justify-between">
                                        <div class="sm:flex">
                                            <p class="flex items-center text-sm text-gray-500">
                                                {{ $order->created_at->format('M d, Y') }}
                                            </p>
                                        </div>
                                        <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                            <p>
                                                {{-- Assuming a formatted_total accessor exists --}}
                                                {{ $order->formatted_total ?? (isset($order->total) ? "@currency(".$order->total.")" : '') }}
                                            </p>
                                        </div>
                                    </div>
                                </a>
                            </li>
                        @empty
                            <li class="p-4 text-center text-gray-500">
                                You have no recent orders.
                            </li>
                        @endforelse
                    </ul>
                </div>
            </div>

            <!-- Recommended Products -->
            <div class="mt-8">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Recommended for You</h3>
                <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    @foreach ($recommendedProducts as $product)
                        <livewire:product-card :product="$product" :key="$product->id" />
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
