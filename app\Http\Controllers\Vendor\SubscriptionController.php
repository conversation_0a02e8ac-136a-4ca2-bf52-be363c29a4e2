<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use App\Models\VendorSubscription;
use App\Services\PaystackService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    protected $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }



    /**
     * Handle the Paystack callback after payment.
     */
    public function handleCallback(Request $request)
    {
        $reference = $request->input('reference');

        if (!$reference) {
            return redirect()->route('vendor.subscription.index')
                ->with('error', 'Subscription failed. No reference provided.');
        }

        $transaction = $this->paystackService->verifyTransaction($reference);

        if ($transaction && $transaction['status'] && $transaction['data']['status'] === 'success') {
            // The actual subscription activation should be handled by webhooks
            // to ensure the subscription code from Paystack is captured correctly.
            // For now, we'll just show a success message.
            return redirect()->route('vendor.subscription.index')
                ->with('success', 'Your subscription is being processed. It will become active shortly.');
        }

        return redirect()->route('vendor.subscription.index')
            ->with('error', $transaction['data']['gateway_response'] ?? 'Subscription verification failed.');
    }

    /**
     * Handle Paystack webhooks.
     */
    public function handleWebhook(Request $request)
    {
        // Verify the webhook signature
        if ($request->header('x-paystack-signature') !== hash_hmac('sha512', $request->getContent(), config('services.paystack.secret'))) {
            abort(401);
        }

        $event = $request->input('event');
        $data = $request->input('data');

        if ($event === 'subscription.create' || $event === 'charge.success') {
            // Use metadata for reliability
            $metadata = $data['metadata'] ?? null;
            $vendorId = $metadata['vendor_id'] ?? null;
            $planId = $metadata['plan_id'] ?? null;

            // Fallback for older webhooks or different flows if metadata is not present
            if (!$vendorId && isset($data['customer']['email'])) {
                $user = \App\Models\User::where('email', $data['customer']['email'])->first();
                if ($user) $vendorId = $user->vendor->id;
            }

            if (!$planId && isset($data['plan']['plan_code'])) {
                $plan = SubscriptionPlan::where('paystack_plan_code', $data['plan']['plan_code'])->first();
                if ($plan) $planId = $plan->id;
            }

            $vendor = \App\Models\Vendor::find($vendorId);
            $plan = SubscriptionPlan::find($planId);

            if ($vendor && $plan) {
                // Deactivate old subscriptions
                $vendor->subscriptions()->where('status', 'active')->update(['status' => 'inactive']);

                // Create the new subscription
                VendorSubscription::create([
                    'vendor_id' => $vendor->id,
                    'subscription_plan_id' => $plan->id,
                    'paystack_subscription_code' => $data['subscription_code'] ?? ($data['authorization']['authorization_code'] ?? 'N/A'),
                    'status' => 'active',
                    'starts_at' => Carbon::parse($data['paid_at'] ?? $data['created_at'] ?? now()),
                    'ends_at' => Carbon::parse($data['next_payment_date'] ?? now()->addDays($plan->duration_days)),
                ]);
            }
        }
        
        // Handle other events like 'subscription.disable', 'invoice.payment_failed', etc.
        if ($event === 'subscription.disable') {
            $subscriptionCode = $data['subscription_code'];
            $subscription = VendorSubscription::where('paystack_subscription_code', $subscriptionCode)->first();
            if ($subscription) {
                $subscription->update(['status' => 'cancelled']);
            }
        }

        return response()->json(['status' => 'success']);
    }


}
