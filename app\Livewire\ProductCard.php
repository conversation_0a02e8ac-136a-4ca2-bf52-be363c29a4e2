<?php

namespace App\Livewire;

use Livewire\Component;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class ProductCard extends Component
{
    public \App\Models\Product $product;

    public $loadingCart = false;
    public $loadingWishlist = false;
    public $inWishlist = false;

    public function mount(\App\Models\Product $product)
    {
        $this->product = $product;
        $this->inWishlist = $this->checkWishlist();
    }

    public function addToCart()
    {
        $this->loadingCart = true;
        // If the product has variants, redirect to the product page to select options.
        if ($this->product->variants()->exists() && $this->product->variants()->count() > 0) {
            return $this->redirect(route('products.show', $this->product->slug));
        }

        // If no variants, add directly to cart.
        $cart = session()->get('cart', []);
        $cartId = $this->product->id;

        if (isset($cart[$cartId])) {
            $cart[$cartId]['quantity']++;
        } else {
            $cart[$cartId] = [
                'id' => $this->product->id,
                'product_id' => $this->product->id,
                'name' => $this->product->name,
                'quantity' => 1,
                'price' => $this->product->price,
                'image_url' => $this->product->image_url,
                'vendor_id' => $this->product->vendor_id,
                'attributes' => []
            ];
        }

        session()->put('cart', $cart);
        $this->dispatch('cartUpdated');
        $this->dispatch('toast', message: 'Product added to cart!', type: 'success');
        $this->loadingCart = false;
    }

    public function addToWishlist()
    {
        $this->loadingWishlist = true;
        if (!Auth::check()) {
            $this->loadingWishlist = false;
            $this->dispatch('toast', message: 'Please login to add to your wishlist.', type: 'info');
            return $this->redirect(route('login'));
        }
        $userId = Auth::id();
        $cacheKey = 'wishlist_' . $userId;
        $wishlist = Cache::get($cacheKey, []);
        if (in_array($this->product->id, $wishlist)) {
            $this->loadingWishlist = false;
            $this->dispatch('toast', message: 'Product is already in your wishlist.', type: 'info');
        } else {
            $wishlist[] = $this->product->id;
            Cache::put($cacheKey, $wishlist, now()->addDays(30));
            $this->inWishlist = true;
            $this->loadingWishlist = false;
            $this->dispatch('wishlistUpdated');
            $this->dispatch('toast', message: 'Product added to wishlist!', type: 'success');
        }
    }

    public function checkWishlist()
    {
        if (!Auth::check()) return false;
        $userId = Auth::id();
        $cacheKey = 'wishlist_' . $userId;
        $wishlist = Cache::get($cacheKey, []);
        return in_array($this->product->id, $wishlist);
    }

    public function render()
    {
        return view('livewire.product-card');
    }
}
