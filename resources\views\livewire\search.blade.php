<div>
    {{-- The whole world belongs to you. --}}
    <div class="relative hidden md:block">
        <form action="{{ route('products.search') }}" method="GET" wire:submit.prevent="performSearch">
            <input 
                wire:model.live.debounce.300ms="query"
                class="py-2 pr-10 pl-4 w-full text-sm rounded-full border focus:outline-none focus:ring-2 focus:ring-black"
                type="search" 
                name="query" 
                placeholder="Search..." 
                autocomplete="off">
            <button type="submit" class="absolute top-0 right-0 mt-2 mr-3">
                <i class="text-gray-500 fas fa-search"></i>
            </button>
        </form>

        @if(!empty($query) && $results->isNotEmpty())
            <div class="absolute z-50 mt-1 w-full max-h-80 bg-white rounded-md border shadow-lg overflow-y-auto">
                <ul>
                    @foreach($results as $product)
                        <li class="border-b border-gray-200 hover:bg-gray-100">
                            <a href="{{ route('products.show', $product->slug) }}" class="flex items-center p-3">
                                <img src="{{ $product->thumbnail_url }}" alt="{{ $product->name }}" class="w-12 h-12 object-cover rounded-md">
                                <div class="ml-3">
                                    <p class="font-semibold text-gray-800">{{ $product->name }}</p>
                                    <p class="text-sm text-gray-600">{{ $product->formatted_price }}</p>
                                </div>
                            </a>
                        </li>
                    @endforeach
                </ul>
            </div>
        @elseif(!empty($query))
            <div class="absolute z-50 p-3 mt-1 w-full bg-white rounded-md border shadow-lg">
                <p class="text-gray-600">No results found for "{{ $query }}".</p>
            </div>
        @endif
    </div>
</div>
