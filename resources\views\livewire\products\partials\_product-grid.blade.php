<div wire:loading.class.delay="opacity-50">
    <div class="flex justify-between items-center mb-4">
        <div class="text-sm text-gray-500">
            @if($products->total() > 0)
                Showing <span class="font-medium text-gray-900">{{ $products->firstItem() }}</span> - <span class="font-medium text-gray-900">{{ $products->lastItem() }}</span> of <span class="font-medium text-gray-900">{{ $products->total() }}</span> results
            @else
                <span class="font-medium text-gray-900">0</span> results
            @endif
        </div>
        <div class="flex items-center">
            <input wire:model.live.debounce.300ms="search" type="text" placeholder="Search products..." class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-black focus:border-black">
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-3 gap-6">
        @forelse($products as $product)
            <livewire:product-card :product="$product" :key="$product->id" />
        @empty
            <div class="col-span-full">
                <div class="flex flex-col items-center justify-center py-16 text-center bg-gray-50 rounded-lg">
                    <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path></svg>
                    <h3 class="mt-4 text-xl font-semibold text-gray-800">No Products Found</h3>
                    <p class="mt-2 text-sm text-gray-500">Sorry, we couldn't find any products matching your criteria. Try adjusting your search or filters.</p>
                    <button wire:click="clearFilters" class="mt-6 px-4 py-2 text-sm font-medium text-white bg-black border border-transparent rounded-md shadow-sm hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">Reset Filters</button>
                </div>
            </div>
        @endforelse
    </div>

    @if($products->hasPages())
    <div class="mt-8">
        {{ $products->links() }}
    </div>
    @endif
</div>
