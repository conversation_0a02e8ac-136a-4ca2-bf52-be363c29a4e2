<div>
    <form wire:submit.prevent="save" class="space-y-8">
        <div class="flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-800">{{ $product->exists ? 'Edit Product' : 'Add New Product' }}</h1>
            <div>
                <a href="{{ route('vendor.products.index') }}" class="text-sm font-medium text-gray-600 hover:text-gray-900">Cancel</a>
                <button type="submit" class="ml-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Save Product
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            {{-- Main Content --}}
            <div class="md:col-span-2 space-y-6">
                {{-- Basic Info --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <h3 class="text-lg font-medium leading-6 text-gray-900">Product Information</h3>
                    <div class="mt-4 space-y-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Product Name</label>
                            <input type="text" wire:model.defer="product.name" id="name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-gray-500 focus:border-gray-500 sm:text-sm">
                            @error('product.name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea wire:model.defer="product.description" id="description" rows="6" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-gray-500 focus:border-gray-500 sm:text-sm"></textarea>
                            @error('product.description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                    </div>
                </div>

                {{-- Variants --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Variants</h3>
                        <button type="button" wire:click="addVariant" class="text-sm font-medium text-gray-600 hover:text-gray-900">+ Add Variant</button>
                    </div>
                    <div class="mt-4 space-y-4">
                        @foreach ($variants as $index => $variant)
                            <div class="p-4 border border-gray-200 rounded-md relative" wire:key="variant-{{ $index }}">
                                <button type="button" wire:click="removeVariant({{ $index }})" class="absolute top-2 right-2 text-gray-400 hover:text-gray-600">
                                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                </button>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Variant Name</label>
                                        <input type="text" wire:model.defer="variants.{{ $index }}.name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="e.g., Color">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Variant Value</label>
                                        <input type="text" wire:model.defer="variants.{{ $index }}.value" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="e.g., Blue">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Additional Price</label>
                                        <input type="number" step="0.01" wire:model.defer="variants.{{ $index }}.price" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="0.00">
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                {{-- Specifications --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Specifications</h3>
                        <button type="button" wire:click="addSpecification" class="text-sm font-medium text-gray-600 hover:text-gray-900">+ Add Specification</button>
                    </div>
                    <div class="mt-4 space-y-4">
                        @foreach ($specifications as $index => $specification)
                            <div class="flex items-center gap-4" wire:key="spec-{{ $index }}">
                                <div class="flex-1">
                                    <input type="text" wire:model.defer="specifications.{{ $index }}.name" class="block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="e.g., Material">
                                </div>
                                <div class="flex-1">
                                    <input type="text" wire:model.defer="specifications.{{ $index }}.value" class="block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="e.g., Cotton">
                                </div>
                                <button type="button" wire:click="removeSpecification({{ $index }})" class="text-gray-400 hover:text-gray-600">
                                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                </button>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            {{-- Sidebar --}}
            <div class="md:col-span-1 space-y-6">
                {{-- Image --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <h3 class="text-lg font-medium leading-6 text-gray-900">Product Image</h3>
                    <div class="mt-4">
                        <div class="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                @if ($image)
                                    <img src="{{ $image->temporaryUrl() }}" class="mx-auto h-24 w-24 object-cover rounded-md">
                                @elseif ($product->image_url)
                                    <img src="{{ Storage::disk('filebase')->url($product->image_url) }}" class="mx-auto h-24 w-24 object-cover rounded-md">
                                @else
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true"><path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" /></svg>
                                @endif
                                <div class="flex text-sm text-gray-600">
                                    <label for="image" class="relative cursor-pointer bg-white rounded-md font-medium text-gray-800 hover:text-gray-700 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-gray-500">
                                        <span>Upload a file</span>
                                        <input id="image" wire:model="image" type="file" class="sr-only">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                        @error('image') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>
                </div>

                {{-- Pricing & Category --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <div class="space-y-4">
                         <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700">Category</label>
                            <select wire:model.defer="product.category_id" id="category_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-gray-500 focus:border-gray-500 sm:text-sm rounded-md">
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                            @error('product.category_id') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700">Price (₦)</label>
                            <input type="number" step="0.01" wire:model.defer="product.price" id="price" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                            @error('product.price') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label for="discount_price" class="block text-sm font-medium text-gray-700">Discount Price (₦)</label>
                            <input type="number" step="0.01" wire:model.defer="product.discount_price" id="discount_price" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                            @error('product.discount_price') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                        <div class="flex items-center">
                            <input id="is_active" wire:model.defer="product.is_active" type="checkbox" class="h-4 w-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">Product is active</label>
                        </div>
                    </div>
                </div>

                {{-- Shipping --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <h3 class="text-lg font-medium leading-6 text-gray-900">Shipping Details</h3>
                    <div class="mt-4 grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Weight (kg)</label>
                            <input type="number" step="0.01" wire:model.defer="product.weight" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Length (cm)</label>
                            <input type="number" step="0.01" wire:model.defer="product.length" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Width (cm)</label>
                            <input type="number" step="0.01" wire:model.defer="product.width" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Height (cm)</label>
                            <input type="number" step="0.01" wire:model.defer="product.height" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
