<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    {{ $product->exists ? 'Edit Product' : 'Create New Product' }}
                </h1>
                <p class="text-gray-300 text-lg">
                    {{ $product->exists ? 'Update your product details and settings' : 'Add a new product to your store inventory' }}
                </p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.products.index') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Cancel</span>
                </a>
                <button type="submit" form="product-form"
                        class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center space-x-2">
                    <i class="fa-solid fa-save transition-transform duration-300 group-hover:scale-110"></i>
                    <span>{{ $product->exists ? 'Update Product' : 'Save Product' }}</span>
                </button>
            </div>
        </div>
    </div>

    <form wire:submit.prevent="save" id="product-form" class="space-y-8">

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {{-- Main Content --}}
            <div class="lg:col-span-2 space-y-8">
                {{-- Basic Information Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-blue-500 rounded-lg">
                                <i class="fas fa-info-circle text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Product Information</h3>
                                <p class="text-gray-600 text-sm">Basic details about your product</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-6">
                        <div class="space-y-2">
                            <label for="name" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Product Name *</label>
                            <input type="text"
                                   wire:model.live="product.name"
                                   id="name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg"
                                   placeholder="Enter product name...">
                            @error('product.name')
                                <p class="text-red-500 text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="space-y-2">
                            <label for="description" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Description *</label>
                            <textarea wire:model.live="product.description"
                                      id="description"
                                      rows="5"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 resize-none"
                                      placeholder="Describe your product in detail..."></textarea>
                            @error('product.description')
                                <p class="text-red-500 text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <label for="price" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Price (₦) *</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 text-lg">₦</span>
                                    </div>
                                    <input type="number"
                                           wire:model.live="product.price"
                                           id="price"
                                           step="0.01"
                                           min="0"
                                           class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg"
                                           placeholder="0.00">
                                </div>
                                @error('product.price')
                                    <p class="text-red-500 text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="discount_price" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Sale Price (₦)</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 text-lg">₦</span>
                                    </div>
                                    <input type="number"
                                           wire:model.live="product.discount_price"
                                           id="discount_price"
                                           step="0.01"
                                           min="0"
                                           class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg"
                                           placeholder="0.00 (Optional)">
                                </div>
                                @error('product.discount_price')
                                    <p class="text-red-500 text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                                @if($product->discount_price && $product->price)
                                    <p class="text-green-600 text-sm flex items-center">
                                        <i class="fas fa-tag mr-1"></i>
                                        {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% discount
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Modern Variants Section --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="p-2 bg-purple-500 rounded-lg">
                                    <i class="fas fa-palette text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">Product Variants</h3>
                                    <p class="text-gray-600 text-sm">Add different options like colors, sizes, etc.</p>
                                </div>
                            </div>
                            <button type="button"
                                    wire:click="addVariant"
                                    class="group inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-xl font-medium hover:bg-purple-600 transition-all duration-300 hover:scale-105">
                                <i class="fas fa-plus mr-2 transition-transform duration-300 group-hover:rotate-90"></i>
                                <span>Add Variant</span>
                            </button>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        @forelse ($variants as $index => $variant)
                            <div class="group p-6 border border-gray-200 rounded-xl hover:border-purple-300 hover:shadow-lg transition-all duration-300 relative" wire:key="variant-{{ $index }}">
                                <button type="button"
                                        wire:click="removeVariant({{ $index }})"
                                        class="absolute top-4 right-4 p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-all duration-300">
                                    <i class="fas fa-times"></i>
                                </button>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 pr-12">
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Variant Type</label>
                                        <input type="text"
                                               wire:model.live="variants.{{ $index }}.name"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300"
                                               placeholder="e.g., Color, Size">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Variant Value</label>
                                        <input type="text"
                                               wire:model.live="variants.{{ $index }}.value"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300"
                                               placeholder="e.g., Red, Large">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Extra Price (₦)</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500">₦</span>
                                            </div>
                                            <input type="number"
                                                   step="0.01"
                                                   wire:model.live="variants.{{ $index }}.price"
                                                   class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300"
                                                   placeholder="0.00">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-palette text-gray-400 text-xl"></i>
                                </div>
                                <p class="text-gray-500 font-medium">No variants added yet</p>
                                <p class="text-gray-400 text-sm mt-1">Add variants like colors, sizes, or styles</p>
                            </div>
                        @endforelse
                    </div>
                </div>

                {{-- Specifications --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Specifications</h3>
                        <button type="button" wire:click="addSpecification" class="text-sm font-medium text-gray-600 hover:text-gray-900">+ Add Specification</button>
                    </div>
                    <div class="mt-4 space-y-4">
                        @foreach ($specifications as $index => $specification)
                            <div class="flex items-center gap-4" wire:key="spec-{{ $index }}">
                                <div class="flex-1">
                                    <input type="text" wire:model.defer="specifications.{{ $index }}.name" class="block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="e.g., Material">
                                </div>
                                <div class="flex-1">
                                    <input type="text" wire:model.defer="specifications.{{ $index }}.value" class="block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="e.g., Cotton">
                                </div>
                                <button type="button" wire:click="removeSpecification({{ $index }})" class="text-gray-400 hover:text-gray-600">
                                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                </button>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            {{-- Sidebar --}}
            <div class="md:col-span-1 space-y-6">
                {{-- Image --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <h3 class="text-lg font-medium leading-6 text-gray-900">Product Image</h3>
                    <div class="mt-4">
                        <div class="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                @if ($image)
                                    <img src="{{ $image->temporaryUrl() }}" class="mx-auto h-24 w-24 object-cover rounded-md">
                                @elseif ($product->image_url)
                                    <img src="{{ $product->image_url }}" class="mx-auto h-24 w-24 object-cover rounded-md">
                                @else
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true"><path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" /></svg>
                                @endif
                                <div class="flex text-sm text-gray-600">
                                    <label for="image" class="relative cursor-pointer bg-white rounded-md font-medium text-gray-800 hover:text-gray-700 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-gray-500">
                                        <span>Upload a file</span>
                                        <input id="image" wire:model="image" type="file" class="sr-only">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                        @error('image') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>
                </div>

                {{-- Pricing & Category --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <div class="space-y-4">
                         <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700">Category</label>
                            <select wire:model.defer="product.category_id" id="category_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-gray-500 focus:border-gray-500 sm:text-sm rounded-md">
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                            @error('product.category_id') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700">Price (₦)</label>
                            <input type="number" step="0.01" wire:model.defer="product.price" id="price" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                            @error('product.price') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label for="discount_price" class="block text-sm font-medium text-gray-700">Discount Price (₦)</label>
                            <input type="number" step="0.01" wire:model.defer="product.discount_price" id="discount_price" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                            @error('product.discount_price') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>
                        <div class="flex items-center">
                            <input id="is_active" wire:model.defer="product.is_active" type="checkbox" class="h-4 w-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">Product is active</label>
                        </div>
                    </div>
                </div>

                {{-- Shipping --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <h3 class="text-lg font-medium leading-6 text-gray-900">Shipping Details</h3>
                    <div class="mt-4 grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Weight (kg)</label>
                            <input type="number" step="0.01" wire:model.defer="product.weight" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Length (cm)</label>
                            <input type="number" step="0.01" wire:model.defer="product.length" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Width (cm)</label>
                            <input type="number" step="0.01" wire:model.defer="product.width" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Height (cm)</label>
                            <input type="number" step="0.01" wire:model.defer="product.height" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
