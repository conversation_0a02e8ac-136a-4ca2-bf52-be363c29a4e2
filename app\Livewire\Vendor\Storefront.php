<?php

namespace App\Livewire\Vendor;

use App\Models\Category;
use App\Models\Product;
use App\Models\Vendor;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Storefront extends Component
{
    use WithPagination;

    public Vendor $vendor;

    public function mount(Vendor $vendor)
    {
        $this->vendor = $vendor;
    }

    public function render()
    {
        $products = Product::query()
            ->where('vendor_id', $this->vendor->id)
            ->where('is_active', true)
            ->with(['vendor', 'category', 'brand'])
            ->latest()
            ->paginate(12);

        $categories = Category::whereHas('products', function($query) {
            $query->where('vendor_id', $this->vendor->id);
        })
        ->withCount(['products' => function($query) {
            $query->where('vendor_id', $this->vendor->id);
        }])
        ->get();

        return view('livewire.vendor.storefront', [
            'products' => $products,
            'categories' => $categories,
        ]);
    }
}
