<?php

namespace App\Livewire\Checkout;

use App\Helpers\Location;
use App\Models\User;
use App\Services\ShipBubbleService;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.app')]
class Index extends Component
{
    public $cartItems = [];
    public $subtotal = 0;

    public $shippingAddress = [
        'address' => '',
        'city' => '',
        'lga' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'NG',
    ];

    public $states = [];
    public $lgas = [];

    public $shippingRates = [];
    public $selectedShippingRate = null;
    public $shippingCost = 0;
    public $total = 0;

    protected $rules = [
        'shippingAddress.address' => 'required|string|max:255',
        'shippingAddress.city' => 'required|string|max:255',
        'shippingAddress.state' => 'required|string|exists:locations,state',
        'shippingAddress.lga' => 'required|string',
        'shippingAddress.postal_code' => 'nullable|string|max:20',
    ];

    public function mount()
    {
        $this->cartItems = session('cart', []);
        if (empty($this->cartItems)) {
            return redirect()->route('cart.index');
        }

        $this->calculateSubtotal();

        /** @var User $user */
        $user = Auth::user();
        if ($user && $user->shippingAddress) {
            $this->shippingAddress = $user->shippingAddress->toArray();
        }

        $this->states = Location::getStates();
        if (!empty($this->shippingAddress['state'])) {
            $this->lgas = Location::getLgas($this->shippingAddress['state']);
        }

        $this->calculateTotal();
    }

    public function updatedShippingAddressState($state)
    {
        $this->lgas = Location::getLgas($state);
        $this->shippingAddress['lga'] = ''; // Reset LGA on state change
        $this->resetShipping();
    }

    public function updatedShippingAddressLga()
    {
        $this->resetShipping();
    }

    public function getShippingRates()
    {
        $this->validate([
            'shippingAddress.address' => 'required|string|max:255',
            'shippingAddress.city' => 'required|string|max:255',
            'shippingAddress.state' => 'required|string',
            'shippingAddress.lga' => 'required|string',
        ]);

        $this->resetShipping();

        try {
            $shipBubbleService = new ShipBubbleService();

            // Prepare shipping address data for Shipbubble
            $shippingAddressData = [
                'name' => Auth::user()->name ?? 'Customer',
                'email' => Auth::user()->email ?? '<EMAIL>',
                'phone' => Auth::user()->phone ?? '08000000000',
                'address' => $this->shippingAddress['address'],
                'city' => $this->shippingAddress['city'],
                'state' => $this->shippingAddress['state'],
                'lga' => $this->shippingAddress['lga'],
                'postal_code' => $this->shippingAddress['postal_code'],
                'country' => $this->shippingAddress['country'] ?? 'NG',
            ];

            // Get the primary vendor for this cart (assuming single vendor for now)
            // In a multi-vendor setup, you'd need to group by vendor and get rates separately
            $vendor = null;
            if (!empty($this->cartItems)) {
                $firstProductId = array_key_first($this->cartItems);
                $product = \App\Models\Product::find($firstProductId);
                $vendor = $product?->vendor;
            }

            // Get shipping rates from Shipbubble
            $response = $shipBubbleService->getShippingRates($this->cartItems, $shippingAddressData, $vendor);

            if (isset($response['status']) && $response['status'] === 'success' && isset($response['data']['couriers'])) {
                // Format the rates for display
                $this->shippingRates = [];
                foreach ($response['data']['couriers'] as $index => $courier) {
                    $this->shippingRates[$index] = [
                        'courier_id' => $courier['courier_id'],
                        'courier_name' => $courier['courier_name'],
                        'service_code' => $courier['service_code'],
                        'total' => $courier['total'],
                        'currency' => $courier['currency'] ?? 'NGN',
                        'delivery_eta' => $courier['delivery_eta'] ?? 'Standard delivery',
                        'service_type' => $courier['service_type'] ?? 'pickup',
                    ];
                }

                // Store the request token for later use when creating shipment
                if (isset($response['data']['request_token'])) {
                    session(['shipbubble_request_token' => $response['data']['request_token']]);
                }
            } else {
                $this->addError('shipping', $response['message'] ?? 'Could not retrieve shipping rates. Please try again.');
            }
        } catch (\Exception $e) {
            \Log::error('Shipbubble rate request failed: ' . $e->getMessage());
            $this->addError('shipping', 'Could not retrieve shipping rates. Please check your address and try again.');
        }
    }

    public function selectShippingRate($rateKey)
    {
        if (isset($this->shippingRates[$rateKey])) {
            $this->selectedShippingRate = $this->shippingRates[$rateKey];
            $this->shippingCost = $this->selectedShippingRate['total'] ?? 0;
            $this->calculateTotal();

            // Store selected shipping info for later use in payment
            session([
                'selected_shipping_rate' => $this->selectedShippingRate,
                'shipping_cost' => $this->shippingCost
            ]);
        }
    }

    private function calculateSubtotal()
    {
        $this->subtotal = collect($this->cartItems)->sum(function ($item) {
            return $item['price'] * $item['quantity'];
        });
    }

    private function calculateTotal()
    {
        $this->total = $this->subtotal + $this->shippingCost;
    }

    private function resetShipping()
    {
        $this->shippingRates = [];
        $this->selectedShippingRate = null;
        $this->shippingCost = 0;
        $this->calculateTotal();
    }

    public function proceedToPayment()
    {
        $this->validate();

        if (!$this->selectedShippingRate) {
            $this->addError('shipping', 'Please select a shipping method.');
            return;
        }

        // Redirect to payment processing
        return redirect()->route('payment.paystack.initialize');
    }

    public function render()
    {
        return view('livewire.checkout.index');
    }
}
