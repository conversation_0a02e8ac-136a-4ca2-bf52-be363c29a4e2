<div class="flex flex-col space-y-6">
    
    <div class="relative w-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden shadow-lg group">
        <img src="<?php echo e($selectedImage['url'] ?? asset('images/product-placeholder.svg')); ?>"
             alt="<?php echo e($selectedImage['alt'] ?? $product->name); ?>"
             class="w-full h-full object-center object-cover aspect-square transition-transform duration-500 hover:scale-105"
             onerror="this.onerror=null; this.src='<?php echo e(asset('images/product-placeholder.svg')); ?>'; this.classList.add('opacity-80');">

        
        <!--[if BLOCK]><![endif]--><?php if($product->is_on_sale): ?>
            <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg">
                <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    SALE
                </span>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        
        <div class="absolute top-4 right-4 bg-white bg-opacity-80 backdrop-blur-sm p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <i class="fas fa-search-plus text-gray-600"></i>
        </div>
    </div>

    
    <!--[if BLOCK]><![endif]--><?php if($images->count() > 1): ?>
        <div class="grid grid-cols-5 gap-3">
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div wire:click="selectImage('<?php echo e($image['url']); ?>')"
                     class="group cursor-pointer rounded-xl overflow-hidden border-2 transition-all duration-300 <?php echo e(($selectedImage['url'] ?? '') === $image['url'] ? 'border-black shadow-lg' : 'border-gray-200 hover:border-gray-400'); ?>">
                    <img src="<?php echo e($image['thumb'] ?? $image['url']); ?>"
                         alt="<?php echo e($image['alt'] ?? $product->name); ?> thumbnail"
                         class="w-full h-full object-center object-cover aspect-square transition-transform duration-300 group-hover:scale-110"
                         onerror="this.onerror=null; this.src='<?php echo e(asset('images/product-placeholder.svg')); ?>';">
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/product/gallery.blade.php ENDPATH**/ ?>