<?php

namespace App\Livewire\Vendor\Earnings;

use App\Models\Order;
use App\Models\Withdrawal;
use App\Services\PaystackService;
use App\Services\ShipBubbleService;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

#[Layout('layouts.vendor')]
class Index extends Component
{
    use WithPagination;

    public $balance = 0;
    public $withdrawableBalance = 0;
    public $totalEarnings = 0;
    public $totalWithdrawn = 0;
    public $banks = [];

    // Withdrawal form fields
    public $amount;
    public $bank_code;
    public $account_name;
    public $account_number;
    public $verifying_account = false;
    public $account_verified = false;

    protected $paystackService;
    protected $shipBubbleService;

    public function boot(PaystackService $paystackService, ShipBubbleService $shipBubbleService)
    {
        $this->paystackService = $paystackService;
        $this->shipBubbleService = $shipBubbleService;
    }

    public function mount()
    {
        $this->loadEarningsData();
        $this->loadBanks();
    }

    public function loadEarningsData()
    {
        $vendor = Auth::user()->vendor;
        $this->balance = $vendor->balance;
        $this->totalEarnings = $vendor->transactions()->where('type', 'sale')->sum('amount');
        $this->totalWithdrawn = abs($vendor->transactions()->where('type', 'withdrawal')->sum('amount'));

        $this->withdrawableBalance = $vendor->transactions()
            ->where('type', 'sale')
            ->whereHasMorph('reference', [Order::class], function ($query) {
                $query->where('status', 'completed');
            })
            ->sum('amount') - $this->totalWithdrawn;
        
        $this->withdrawableBalance = max(0, $this->withdrawableBalance);
    }

    public function loadBanks()
    {
        $bankListResponse = $this->paystackService->getBankList();
        if ($bankListResponse['status']) {
            $this->banks = $bankListResponse['data'];
        }
    }

    public function withdraw()
    {
        $vendor = Auth::user()->vendor;
        $this->loadEarningsData(); // Recalculate just before validation

        // ShipBubble delivery check
        $orders = $vendor->orders()->where('status', 'completed')->whereNotNull('shipbubble_token')->get();
        foreach ($orders as $order) {
            $trackingCode = $order->shipbubble_token;
            $tracking = $this->shipBubbleService->trackShipment($trackingCode);
            if (!isset($tracking['status_code']) || $tracking['status_code'] !== 'delivered') {
                session()->flash('error', 'Withdrawal is only allowed after all your completed orders have been delivered by ShipBubble.');
                return;
            }
        }

        $this->validate([
            'amount' => ['required', 'numeric', 'min:1000', function ($attribute, $value, $fail) {
                if ($value > $this->withdrawableBalance) {
                    $fail('The withdrawal amount exceeds your available balance from completed orders.');
                }
            }],
            'bank_code' => 'required|string|max:255',
            'account_name' => 'required|string|max:255',
            'account_number' => 'required|string|digits:10',
        ]);

        $bankName = '';
        if (!empty($this->banks)) {
            $selectedBank = collect($this->banks)->firstWhere('code', $this->bank_code);
            if ($selectedBank) {
                $bankName = $selectedBank['name'];
            }
        }

        if (empty($bankName)) {
            session()->flash('error', 'Invalid bank selected.');
            return;
        }

        Withdrawal::create([
            'vendor_id' => $vendor->id,
            'amount' => $this->amount,
            'method' => 'bank_transfer',
            'details' => [
                'bank_code' => $this->bank_code,
                'bank_name' => $bankName,
                'account_name' => $this->account_name,
                'account_number' => $this->account_number,
            ],
            'status' => 'pending',
        ]);

        $this->reset(['amount', 'bank_code', 'account_name', 'account_number', 'verifying_account', 'account_verified']);
        $this->loadEarningsData();
        
        session()->flash('success', 'Your withdrawal request has been submitted and is pending approval.');

        $this->dispatch('close-modal');
    }

    /**
     * Auto-verify account when account number and bank code are provided
     */
    public function updatedAccountNumber()
    {
        $this->verifyAccount();
    }

    public function updatedBankCode()
    {
        $this->verifyAccount();
    }

    private function verifyAccount()
    {
        // Reset verification state
        $this->account_verified = false;
        $this->account_name = '';

        // Only verify if we have both bank code and a 10-digit account number
        if (empty($this->bank_code) || empty($this->account_number) || strlen($this->account_number) !== 10) {
            return;
        }

        try {
            $this->verifying_account = true;

            // Call Paystack API to verify account
            $response = $this->paystackService->resolveAccountName($this->account_number, $this->bank_code);

            if ($response && isset($response['data']['account_name'])) {
                $this->account_name = $response['data']['account_name'];
                $this->account_verified = true;

                // Show success message
                $this->dispatch('account-verified', [
                    'message' => 'Account verified successfully!',
                    'account_name' => $this->account_name
                ]);
            } else {
                $this->dispatch('account-verification-failed', [
                    'message' => 'Could not verify account. Please check your account number and bank.'
                ]);
            }
        } catch (\Exception $e) {
            $this->dispatch('account-verification-failed', [
                'message' => 'Account verification failed. Please check your details and try again.'
            ]);
        } finally {
            $this->verifying_account = false;
        }
    }

    public function render()
    {
        $transactions = Auth::user()->vendor->transactions()->latest()->paginate(15);

        return view('livewire.vendor.earnings.index', [
            'transactions' => $transactions,
        ]);
    }
}
