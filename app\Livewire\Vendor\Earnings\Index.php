<?php

namespace App\Livewire\Vendor\Earnings;

use App\Models\Order;
use App\Models\Withdrawal;
use App\Services\PaystackService;
use App\Services\ShipBubbleService;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

#[Layout('layouts.vendor')]
class Index extends Component
{
    use WithPagination;

    public $balance = 0;
    public $withdrawableBalance = 0;
    public $totalEarnings = 0;
    public $totalWithdrawn = 0;
    public $banks = [];

    // Withdrawal form fields
    public $amount;
    public $bank_code;
    public $account_name;
    public $account_number;

    protected $paystackService;
    protected $shipBubbleService;

    public function boot(PaystackService $paystackService, ShipBubbleService $shipBubbleService)
    {
        $this->paystackService = $paystackService;
        $this->shipBubbleService = $shipBubbleService;
    }

    public function mount()
    {
        $this->loadEarningsData();
        $this->loadBanks();
    }

    public function loadEarningsData()
    {
        $vendor = Auth::user()->vendor;
        $this->balance = $vendor->balance;
        $this->totalEarnings = $vendor->transactions()->where('type', 'sale')->sum('amount');
        $this->totalWithdrawn = abs($vendor->transactions()->where('type', 'withdrawal')->sum('amount'));

        $this->withdrawableBalance = $vendor->transactions()
            ->where('type', 'sale')
            ->whereHasMorph('reference', [Order::class], function ($query) {
                $query->where('status', 'completed');
            })
            ->sum('amount') - $this->totalWithdrawn;
        
        $this->withdrawableBalance = max(0, $this->withdrawableBalance);
    }

    public function loadBanks()
    {
        $bankListResponse = $this->paystackService->getBankList();
        if ($bankListResponse['status']) {
            $this->banks = $bankListResponse['data'];
        }
    }

    public function withdraw()
    {
        $vendor = Auth::user()->vendor;
        $this->loadEarningsData(); // Recalculate just before validation

        // ShipBubble delivery check
        $orders = $vendor->orders()->where('status', 'completed')->whereNotNull('shipbubble_token')->get();
        foreach ($orders as $order) {
            $trackingCode = $order->shipbubble_token;
            $tracking = $this->shipBubbleService->trackShipment($trackingCode);
            if (!isset($tracking['status_code']) || $tracking['status_code'] !== 'delivered') {
                session()->flash('error', 'Withdrawal is only allowed after all your completed orders have been delivered by ShipBubble.');
                return;
            }
        }

        $this->validate([
            'amount' => ['required', 'numeric', 'min:1000', function ($attribute, $value, $fail) {
                if ($value > $this->withdrawableBalance) {
                    $fail('The withdrawal amount exceeds your available balance from completed orders.');
                }
            }],
            'bank_code' => 'required|string|max:255',
            'account_name' => 'required|string|max:255',
            'account_number' => 'required|string|digits:10',
        ]);

        $bankName = '';
        if (!empty($this->banks)) {
            $selectedBank = collect($this->banks)->firstWhere('code', $this->bank_code);
            if ($selectedBank) {
                $bankName = $selectedBank['name'];
            }
        }

        if (empty($bankName)) {
            session()->flash('error', 'Invalid bank selected.');
            return;
        }

        Withdrawal::create([
            'vendor_id' => $vendor->id,
            'amount' => $this->amount,
            'method' => 'bank_transfer',
            'details' => [
                'bank_code' => $this->bank_code,
                'bank_name' => $bankName,
                'account_name' => $this->account_name,
                'account_number' => $this->account_number,
            ],
            'status' => 'pending',
        ]);

        $this->reset(['amount', 'bank_code', 'account_name', 'account_number']);
        $this->loadEarningsData();
        
        session()->flash('success', 'Your withdrawal request has been submitted and is pending approval.');

        $this->dispatch('close-modal');
    }

    public function render()
    {
        $transactions = Auth::user()->vendor->transactions()->latest()->paginate(15);

        return view('livewire.vendor.earnings.index', [
            'transactions' => $transactions,
        ]);
    }
}
