<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Earnings & Withdrawals
                </h1>
                <p class="text-gray-300 text-lg">Manage your earnings and request withdrawals</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.dashboard') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Modern Alert Messages --}}
    @if (session()->has('success'))
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 text-green-700 p-6 mb-8 rounded-r-2xl shadow-lg" role="alert">
            <div class="flex items-center">
                <div class="p-2 bg-green-500 rounded-full mr-4">
                    <i class="fas fa-check text-white"></i>
                </div>
                <p class="font-semibold">{{ session('success') }}</p>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-gradient-to-r from-red-50 to-pink-50 border-l-4 border-red-500 text-red-700 p-6 mb-8 rounded-r-2xl shadow-lg" role="alert">
            <div class="flex items-center">
                <div class="p-2 bg-red-500 rounded-full mr-4">
                    <i class="fas fa-exclamation-triangle text-white"></i>
                </div>
                <p class="font-semibold">{{ session('error') }}</p>
            </div>
        </div>
    @endif

    {{-- Modern Stats Grid --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {{-- Total Balance Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-wallet text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-blue-100 text-blue-800 transition-all duration-300">
                        Total
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Balance</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    @currency($balance)
                </p>
                <p class="text-xs text-gray-400">from all transactions</p>
            </div>
        </div>

        {{-- Withdrawable Balance Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-money-bill-wave text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-green-100 text-green-800 transition-all duration-300">
                        Available
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Withdrawable Balance</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">
                    @currency($withdrawableBalance)
                </p>
                <div class="mt-4">
                    <button x-data @click="$dispatch('open-modal')"
                            class="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold py-3 px-4 rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 hover:scale-105 shadow-lg">
                        <i class="fas fa-download mr-2"></i>
                        Withdraw Funds
                    </button>
                </div>
            </div>
        </div>

        {{-- Total Withdrawn Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-arrow-down text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-purple-100 text-purple-800 transition-all duration-300">
                        Withdrawn
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Withdrawn</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">
                    @currency($totalWithdrawn)
                </p>
                <p class="text-xs text-gray-400">successfully withdrawn</p>
            </div>
        </div>

        {{-- Total Earnings Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chart-line text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 transition-all duration-300">
                        Lifetime
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Earnings</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">
                    @currency($totalEarnings)
                </p>
                <p class="text-xs text-gray-400">lifetime earnings</p>
            </div>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b">
            <h5 class="font-bold text-lg">Transaction History</h5>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full leading-normal">
                <thead>
                    <tr class="border-b bg-gray-50">
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                        <th class="px-5 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
                        <th class="px-5 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Balance</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($transactions as $transaction)
                        <tr class="hover:bg-gray-50">
                            <td class="px-5 py-5 border-b border-gray-200 text-sm">{{ $transaction->created_at->format('M d, Y H:i') }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $transaction->amount > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }} text-capitalize">
                                    {{ $transaction->type }}
                                </span>
                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm">{{ $transaction->description }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm text-right font-semibold {{ $transaction->amount < 0 ? 'text-red-600' : 'text-green-600' }}">
                                ₦{{ number_format(abs($transaction->amount), 2) }}
                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm text-right">₦{{ number_format($transaction->balance_after, 2) }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center py-10 text-gray-500">No transactions found.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4">
            {{ $transactions->links() }}
        </div>
    </div>

    <!-- Withdrawal Modal -->
    <div x-data="{ show: false }" @open-modal.window="show = true" @close-modal.window="show = false" x-show="show" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto" @click.away="show = false">
            <form wire:submit.prevent="withdraw">
                <h5 class="text-lg font-bold mb-4">Request Bank Transfer</h5>
                <div class="bg-blue-50 border border-blue-200 text-blue-800 text-sm p-3 rounded mb-4">
                    Withdrawals are processed via bank transfer to Nigerian bank accounts. Minimum withdrawal is ₦1,000.
                </div>
                <div class="mb-4">
                    <label for="amount" class="block text-sm font-medium text-gray-700">Amount (₦)</label>
                    <input type="number" wire:model.defer="amount" id="amount" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-black focus:ring-black sm:text-sm" step="100" min="1000" required>
                    <p class="text-xs text-gray-500 mt-1">Withdrawable balance: ₦{{ number_format($withdrawableBalance, 2) }}</p>
                    @error('amount') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
                <div class="mb-4">
                    <label for="bank_code" class="block text-sm font-medium text-gray-700">Bank Name</label>
                    <select wire:model.defer="bank_code" id="bank_code" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-black focus:ring-black sm:text-sm" required>
                        <option value="">Select Bank</option>
                        @foreach($banks as $bank)
                            <option value="{{ $bank['code'] }}">{{ $bank['name'] }}</option>
                        @endforeach
                    </select>
                    @error('bank_code') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
                <div class="mb-4">
                    <label for="account_name" class="block text-sm font-medium text-gray-700">Account Name</label>
                    <input type="text" wire:model.defer="account_name" id="account_name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-black focus:ring-black sm:text-sm" required>
                     @error('account_name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
                <div class="mb-4">
                    <label for="account_number" class="block text-sm font-medium text-gray-700">Account Number</label>
                    <input type="text" wire:model.defer="account_number" id="account_number" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-black focus:ring-black sm:text-sm" required pattern="\d{10}" title="Please enter a 10-digit account number">
                     @error('account_number') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
                <div class="flex justify-end space-x-4">
                    <button type="button" @click="show = false" class="bg-gray-200 text-gray-800 font-bold py-2 px-4 rounded hover:bg-gray-300 transition duration-300">Cancel</button>
                    <button type="submit" class="bg-black text-white font-bold py-2 px-4 rounded hover:bg-gray-800 transition duration-300">
                        <span wire:loading.remove>Request Withdrawal</span>
                        <span wire:loading>Processing...</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
