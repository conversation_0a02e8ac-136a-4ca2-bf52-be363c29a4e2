<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $product && $product->exists ? 'Edit Product' : 'Add New Product' }}</h1>
            <a href="{{ route('admin.products.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">Back to Products</a>
        </div>

        <form wire:submit.prevent="save">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 space-y-6">
                        <x-input wire:model.defer="name" label="Product Name" placeholder="Enter product name" />
                        <x-textarea wire:model.defer="description" label="Description" placeholder="Enter product description" />
                        <x-input wire:model.defer="slug" label="Slug" placeholder="enter-product-slug" />
                    </div>
                </div>

                <div class="space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 space-y-6">
                        <x-input wire:model.defer="price" label="Price" type="number" step="0.01" placeholder="0.00" />
                        <x-input wire:model.defer="discount_price" label="Discount Price" type="number" step="0.01" placeholder="0.00" />
                        <x-input wire:model.defer="stock" label="Stock Quantity" type="number" placeholder="0" />
                    </div>

                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 space-y-6">
                        <x-select
                            label="Vendor"
                            wire:model.defer="vendor_id"
                            placeholder="Select a vendor"
                            :options="$vendors"
                            option-label="shop_name"
                            option-value="id"
                        />
                        <x-select
                            label="Category"
                            wire:model.defer="category_id"
                            placeholder="Select a category"
                            :options="$categories"
                            option-label="name"
                            option-value="id"
                        />
                    </div>

                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                        <x-input wire:model.defer="image_url" label="Image URL" placeholder="https://example.com/image.png" />
                        <x-checkbox wire:model.defer="is_active" label="Active" class="mt-4" />
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end">
                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    {{ $product && $product->exists ? 'Update Product' : 'Create Product' }}
                </button>
            </div>
        </form>
    </div>
</div>
