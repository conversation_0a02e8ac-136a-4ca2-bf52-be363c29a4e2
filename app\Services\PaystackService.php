<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class PaystackService
{
    protected $secretKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->secretKey = config('services.paystack.secret');
        $this->baseUrl = 'https://api.paystack.co';
    }

    /**
     * Initialize a transaction.
     *
     * @param array $data
     * @return array
     */
    public function initializeTransaction(array $data)
    {
        $payload = [
            'email' => $data['email'],
            'amount' => $data['amount'], // Amount should be in kobo
            'reference' => $data['reference'],
            'callback_url' => $data['callback_url'],
        ];

        if (isset($data['plan']) && $data['plan']) {
            $payload['plan'] = $data['plan'];
        }

        if (isset($data['metadata']) && is_array($data['metadata'])) {
            $payload['metadata'] = $data['metadata'];
        }

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/transaction/initialize', $payload);

        return $response->json();
    }

    /**
     * Verify a transaction.
     *
     * @param string $reference
     * @return array
     */
    public function verifyTransaction(string $reference)
    {
        $response = Http::withToken($this->secretKey)
            ->get($this->baseUrl . '/transaction/verify/' . $reference);

        return $response->json();
    }

    /**
     * Create a subscription plan on Paystack.
     *
     * @param array $data
     * @return array
     */
    public function createPlan(array $data)
    {
        $payload = [
            'name' => $data['name'],
            'amount' => $data['amount'], // Amount should be in kobo
            'interval' => $data['interval'], // e.g., monthly, annually
            'description' => $data['description'] ?? null,
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/plan', $payload);

        return $response->json();
    }

    /**
     * Update a subscription plan on Paystack.
     *
     * @param string $planCode
     * @param array $data
     * @return array
     */
    public function updatePlan(string $planCode, array $data)
    {
        $payload = [
            'name' => $data['name'],
            'amount' => $data['amount'], // Amount should be in kobo
            'description' => $data['description'] ?? null,
        ];

        $response = Http::withToken($this->secretKey)
            ->put($this->baseUrl . '/plan/' . $planCode, $payload);

        return $response->json();
    }

    /**
     * Create a transfer recipient.
     *
     * @param array $data
     * @return array
     */
    public function createTransferRecipient(array $data)
    {
        $payload = [
            'type' => 'nuban',
            'name' => $data['name'],
            'account_number' => $data['account_number'],
            'bank_code' => $data['bank_code'],
            'currency' => 'NGN',
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/transferrecipient', $payload);

        return $response->json();
    }

    /**
     * Initiate a transfer to a recipient.
     *
     * @param array $data
     * @return array
     */
    public function initiateTransfer(array $data)
    {
        $payload = [
            'source' => 'balance',
            'amount' => $data['amount'], // Amount should be in kobo
            'recipient' => $data['recipient'], // Recipient code
            'reason' => $data['reason'],
            'reference' => $data['reference'] ?? null,
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/transfer', $payload);

        return $response->json();
    }

    /**
     * Get the list of supported banks.
     *
     * @return array
     */
    public function getBankList()
    {
        $response = Http::withToken($this->secretKey)
            ->get($this->baseUrl . '/bank?currency=NGN');

        return $response->json();
    }

    public function resolveAccountName($accountNumber, $bankCode)
    {
        $response = \Illuminate\Support\Facades\Http::withToken($this->secretKey)
            ->get($this->baseUrl . '/bank/resolve', [
                'account_number' => $accountNumber,
                'bank_code' => $bankCode,
            ]);
        return $response->json();
    }
}
