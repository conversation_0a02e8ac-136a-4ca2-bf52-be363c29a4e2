<?php

namespace App\Livewire\Admin\Users;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Url;

class Index extends Component
{
    use WithPagination;

    #[Url(except: '')]
    public string $search = '';

    #[Url]
    public string $sortField = 'created_at';

    #[Url]
    public string $sortDirection = 'desc';

    public int $perPage = 10;

    public ?User $deleting = null;

    public function sortBy(string $field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
        $this->resetPage();
    }

    public function confirmDelete(User $user): void
    {
        $this->deleting = $user;
    }

    public function deleteUser(): void
    {
        if ($this->deleting) {
            if (auth()->id() === $this->deleting->id) {
                session()->flash('error', 'You cannot delete your own account.');
                $this->deleting = null;
                return;
            }

            if ($this->deleting->hasRole('admin') && User::role('admin')->count() === 1) {
                 session()->flash('error', 'Cannot delete the last admin user.');
                 $this->deleting = null;
                 return;
            }

            $this->deleting->delete();
            session()->flash('success', 'User deleted successfully.');
        }
        $this->deleting = null;
    }


    public function render()
    {
        $users = User::with('roles')
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%');
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        return view('livewire.admin.users.index', [
            'users' => $users,
        ])->layout('layouts.admin');
    }
}
