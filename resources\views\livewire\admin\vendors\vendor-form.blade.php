<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $vendor && $vendor->exists ? 'Edit Vendor' : 'Add New Vendor' }}</h1>
            <a href="{{ route('admin.vendors.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">Back to Vendors</a>
        </div>

        <form wire:submit.prevent="save">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2 space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">User Account Details</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <x-input wire:model.defer="name" label="Full Name" placeholder="Enter user's full name" />
                            <x-input wire:model.defer="email" label="Email Address" type="email" placeholder="Enter user's email" />
                            <x-input wire:model.defer="password" label="Password" type="password" placeholder="Enter new password" />
                            <x-input wire:model.defer="password_confirmation" label="Confirm Password" type="password" placeholder="Confirm new password" />
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Shop Details</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <x-input wire:model.defer="shop_name" label="Shop Name" placeholder="Enter shop name" />
                            <x-input wire:model.defer="shop_email" label="Shop Email" type="email" placeholder="Enter shop contact email" />
                            <x-input wire:model.defer="shop_phone" label="Shop Phone" placeholder="Enter shop phone number" />
                        </div>
                        <div class="mt-6">
                            <x-textarea wire:model.defer="address" label="Shop Address" placeholder="Enter shop address" />
                        </div>
                    </div>
                </div>

                <div class="space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Status & Visibility</h2>
                        <div class="space-y-4">
                            <x-checkbox wire:model.defer="is_approved" label="Approved" />
                            <x-checkbox wire:model.defer="is_featured" label="Featured" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end">
                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    {{ $vendor && $vendor->exists ? 'Update Vendor' : 'Create Vendor' }}
                </button>
            </div>
        </form>
    </div>
</div>
