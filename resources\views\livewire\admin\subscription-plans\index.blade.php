<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Subscription Plans</h1>
            <x-button primary label="Add Plan" wire:click="create" />
        </div>

        <x-session-message />

        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700/50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Price</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Product Limit</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Commission Rate</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($plans as $plan)
                            <tr wire:key="{{ $plan->id }}">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $plan->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">${{ number_format($plan->price, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $plan->duration_days }} days</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $plan->product_limit ?? 'Unlimited' }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $plan->commission_rate ?? 0 }}%</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <x-badge :label="$plan->is_active ? 'Active' : 'Inactive'" :color="$plan->is_active ? 'positive' : 'secondary'" />
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <x-button.circle primary icon="pencil" wire:click="edit({{ $plan->id }})" />
                                    <x-button.circle negative icon="trash" wire:click="delete({{ $plan->id }})" wire:confirm="Are you sure you want to delete this plan?" />
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center text-sm text-gray-500 dark:text-gray-400">No subscription plans found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <div class="mt-6">
            {{ $plans->links() }}
        </div>

        <!-- Create/Edit Modal -->
        <x-modal wire:model.defer="showModal">
            <x-card :title="$editing ? 'Edit Subscription Plan' : 'Create Subscription Plan'">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <x-input label="Plan Name" wire:model="name" />
                    </div>
                    <div class="md:col-span-2">
                        <x-textarea label="Description" wire:model="description" />
                    </div>
                    <x-input label="Price" wire:model="price" type="number" step="0.01" min="0" prefix="$" />
                    <x-select
                        label="Interval"
                        wire:model="interval"
                        placeholder="Select an interval"
                        :options="[
                            ['name' => 'Monthly', 'id' => 'monthly'],
                            ['name' => 'Quarterly', 'id' => 'quarterly'],
                            ['name' => 'Annually', 'id' => 'annually'],
                        ]"
                        option-label="name"
                        option-value="id"
                    />
                    <x-input label="Duration (days)" wire:model="duration_days" type="number" min="1" />
                    <x-input label="Product Limit" wire:model="product_limit" type="number" min="0" hint="Leave empty for unlimited" />
                    <x-input label="Commission Rate (%)" wire:model="commission_rate" type="number" step="0.01" min="0" max="100" />
                    <div class="md:col-span-2">
                        <x-toggle lg label="Active" wire:model="is_active" />
                    </div>
                    <div class="md:col-span-2">
                        <x-textarea label="Features" wire:model="features" hint="Enter each feature on a new line" />
                    </div>
                </div>

                <x-slot name="footer">
                    <div class="flex justify-end gap-x-4">
                        <x-button flat label="Cancel" x-on:click="close" />
                        <x-button primary label="Save" wire:click="save" />
                    </div>
                </x-slot>
            </x-card>
        </x-modal>
    </div>
</div>
