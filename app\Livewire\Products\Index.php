<?php

namespace App\Livewire\Products;

use App\Models\Category;
use App\Models\Product;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Url;

class Index extends Component
{
    use WithPagination;

    protected string $paginationTheme = 'tailwind';

    #[Url(except: '')]
    public string $search = '';

    #[Url(except: '')]
    public string $category = '';

    #[Url(except: '')]
    public ?int $min_price = null;

    #[Url(except: '')]
    public ?int $max_price = null;

    #[Url(except: 'latest')]
    public string $sort_by = 'latest';

    public function updating($key): void
    {
        if (in_array($key, ['search', 'category', 'min_price', 'max_price', 'sort_by'])) {
            $this->resetPage();
        }
    }

    public function render()
    {
        $productsQuery = Product::query()->with(['category', 'brand']);

        if ($this->search) {
            $productsQuery->where('name', 'like', '%' . $this->search . '%');
        }

        if ($this->category) {
            $productsQuery->whereHas('category', function ($query) {
                $query->where('slug', $this->category);
            });
        }

        if ($this->min_price) {
            $productsQuery->where('price', '>=', $this->min_price);
        }

        if ($this->max_price) {
            $productsQuery->where('price', '<=', $this->max_price);
        }

        match ($this->sort_by) {
            'latest' => $productsQuery->latest(),
            'price_asc' => $productsQuery->orderBy('price', 'asc'),
            'price_desc' => $productsQuery->orderBy('price', 'desc'),
            'name_asc' => $productsQuery->orderBy('name', 'asc'),
            'name_desc' => $productsQuery->orderBy('name', 'desc'),
            default => $productsQuery->latest(),
        };

        $products = $productsQuery->paginate(12);
        $categories = Category::whereNull('parent_id')->with('children')->get();

        return view('livewire.products.index', [
            'products' => $products,
            'categories' => $categories,
        ]);
    }

    public function clearFilters()
    {
        $this->reset();
    }
}
