<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="max-w-7xl mx-auto pt-8 pb-24 px-4 sm:px-6 lg:px-8">
        <!-- Header Section -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Shopping Cart</h1>
            <p class="text-lg text-gray-600">Review your items and proceed to checkout</p>
        </div>

        <div class="lg:grid lg:grid-cols-12 lg:gap-x-12 lg:items-start xl:gap-x-16">
            <section aria-labelledby="cart-heading" class="lg:col-span-8">
                <h2 id="cart-heading" class="sr-only">Items in your shopping cart</h2>

                <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                    <div class="px-6 py-8">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-semibold text-gray-900">Cart Items (<?php echo e($cartItems->count()); ?>)</h3>
                            <!--[if BLOCK]><![endif]--><?php if($cartItems->isNotEmpty()): ?>
                                <button type="button" wire:click="clearCart" wire:confirm="Are you sure you want to clear your cart?"
                                        class="text-sm text-red-600 hover:text-red-800 font-medium transition-colors">
                                    Clear Cart
                                </button>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <ul role="list" class="divide-y divide-gray-200">
                            <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <li class="py-8 first:pt-0 last:pb-0">
                                    <div class="flex items-center space-x-6">
                                        <!-- Product Image -->
                                        <div class="flex-shrink-0">
                                            <img src="<?php echo e($item['image_url'] ?? asset('img/default-product.png')); ?>"
                                                 alt="<?php echo e($item['name']); ?>"
                                                 class="w-24 h-24 rounded-xl object-cover shadow-md hover:shadow-lg transition-shadow">
                                        </div>

                                        <!-- Product Details -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <h3 class="text-lg font-semibold text-gray-900 mb-1"><?php echo e($item['name']); ?></h3>
                                                    <p class="text-2xl font-bold text-gray-900">₦<?php echo e(number_format($item['price'], 2)); ?></p>
                                                    <p class="text-sm text-gray-500 mt-1">Unit price</p>
                                                </div>

                                                <!-- Remove Button -->
                                                <button type="button" wire:click="removeItem('<?php echo e($item['id'] ?? ''); ?>')"
                                                        class="p-2 text-gray-400 hover:text-red-500 transition-colors rounded-full hover:bg-red-50">
                                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </div>

                                            <!-- Quantity and Subtotal -->
                                            <div class="flex items-center justify-between mt-4">
                                                <div class="flex items-center space-x-3">
                                                    <label class="text-sm font-medium text-gray-700">Quantity:</label>
                                                    <input type="number" min="1" max="99"
                                                           wire:change="updateQuantity('<?php echo e($item['id'] ?? ''); ?>', $event.target.value)"
                                                           value="<?php echo e($item['quantity']); ?>"
                                                           class="w-20 rounded-lg border-gray-300 text-sm font-medium focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                </div>

                                                <div class="text-right">
                                                    <p class="text-lg font-bold text-gray-900">₦<?php echo e(number_format($item['price'] * $item['quantity'], 2)); ?></p>
                                                    <p class="text-sm text-gray-500">Subtotal</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <li class="text-center py-16">
                                    <div class="max-w-sm mx-auto">
                                        <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                        </svg>
                                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Your cart is empty</h3>
                                        <p class="text-gray-500 mb-6">Start shopping to add items to your cart</p>
                                        <a href="<?php echo e(route('products.index')); ?>"
                                           class="inline-flex items-center px-6 py-3 bg-black text-white font-medium rounded-full hover:bg-gray-800 transition-colors">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                            </svg>
                                            Continue Shopping
                                        </a>
                                    </div>
                                </li>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Order Summary -->
            <section aria-labelledby="summary-heading" class="lg:col-span-4">
                <div class="bg-white rounded-2xl shadow-xl overflow-hidden sticky top-8">
                    <div class="px-6 py-8">
                        <h2 id="summary-heading" class="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>

                        <!--[if BLOCK]><![endif]--><?php if($cartItems->isNotEmpty()): ?>
                            <dl class="space-y-4">
                                <div class="flex items-center justify-between py-2">
                                    <dt class="text-base text-gray-600">Subtotal (<?php echo e($cartItems->count()); ?> items)</dt>
                                    <dd class="text-base font-semibold text-gray-900">₦<?php echo e(number_format($subtotal, 2)); ?></dd>
                                </div>

                                <div class="flex items-center justify-between py-2 text-sm text-gray-500">
                                    <dt>Shipping</dt>
                                    <dd>Calculated at checkout</dd>
                                </div>

                                <div class="border-t border-gray-200 pt-4 flex items-center justify-between">
                                    <dt class="text-lg font-bold text-gray-900">Total</dt>
                                    <dd class="text-lg font-bold text-gray-900">₦<?php echo e(number_format($total, 2)); ?></dd>
                                </div>
                            </dl>

                            <div class="mt-8 space-y-4">
                                <a href="<?php echo e(route('checkout.index')); ?>"
                                   class="w-full bg-black text-white py-4 px-6 rounded-xl font-semibold text-center hover:bg-gray-800 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 flex items-center justify-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
                                    </svg>
                                    Proceed to Checkout
                                </a>

                                <a href="<?php echo e(route('products.index')); ?>"
                                   class="w-full bg-gray-100 text-gray-900 py-3 px-6 rounded-xl font-medium text-center hover:bg-gray-200 transition-colors">
                                    Continue Shopping
                                </a>
                            </div>

                            <!-- Security Badge -->
                            <div class="mt-6 flex items-center justify-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Secure checkout powered by Paystack
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <p class="text-gray-500 mb-4">Your cart is empty</p>
                                <a href="<?php echo e(route('products.index')); ?>"
                                   class="inline-flex items-center px-6 py-3 bg-black text-white font-medium rounded-full hover:bg-gray-800 transition-colors">
                                    Start Shopping
                                </a>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </section>
        </div>

        <!-- Recommended Products -->
        <!--[if BLOCK]><![endif]--><?php if($recommendedProducts->isNotEmpty()): ?>
            <section aria-labelledby="related-products-heading" class="mt-20">
                <div class="text-center mb-12">
                    <h2 id="related-products-heading" class="text-3xl font-bold text-gray-900 mb-4">You May Also Like</h2>
                    <p class="text-lg text-gray-600">Discover more amazing products</p>
                </div>

                <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $recommendedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="group relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                            <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200">
                                <img src="<?php echo e($product->image_url ?? asset('img/default-product.png')); ?>"
                                     alt="<?php echo e($product->name); ?>"
                                     class="h-64 w-full object-cover object-center group-hover:scale-105 transition-transform duration-300">
                            </div>

                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                                    <a href="<?php echo e(route('products.show', $product->slug)); ?>">
                                        <span aria-hidden="true" class="absolute inset-0"></span>
                                        <?php echo e($product->name); ?>

                                    </a>
                                </h3>
                                <p class="text-xl font-bold text-gray-900">₦<?php echo e(number_format($product->price, 2)); ?></p>

                                <!-- Quick Add to Cart -->
                                <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('add-to-cart-button', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, 'cart-rec-'.$product->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </section>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/cart/index.blade.php ENDPATH**/ ?>