<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Shipping Settings
                </h1>
                <p class="text-gray-300 text-lg">Manage your shipping addresses and package configurations</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.dashboard') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {{-- Default Shipping Address --}}
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-500">
            <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="p-3 bg-blue-500 rounded-xl shadow-lg">
                            <i class="fas fa-map-marker-alt text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">Default Shipping Address</h3>
                            <p class="text-gray-600 text-sm">Your primary shipping location</p>
                        </div>
                    </div>
                    <a href="{{ route('vendor.settings.index') }}#shipping"
                       class="group bg-blue-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-600 transition-all duration-300 hover:scale-105 flex items-center space-x-2">
                        <i class="fas fa-edit transition-transform duration-300 group-hover:scale-110"></i>
                        <span>Edit</span>
                    </a>
                </div>
            </div>
            <div class="p-6">
                @if($defaultAddress)
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mt-1">
                                <i class="fas fa-check text-green-600 text-sm"></i>
                            </div>
                            <address class="not-italic text-gray-700 leading-relaxed">
                                <div class="font-semibold text-gray-900 text-lg mb-2">{{ $defaultAddress->recipient_name ?? 'Default Address' }}</div>
                                <div class="space-y-1">
                                    <div>{{ $defaultAddress->line1 ?? $defaultAddress->address ?? 'Address not set' }}</div>
                                    @if($defaultAddress->line2 ?? false)<div>{{ $defaultAddress->line2 }}</div>@endif
                                    <div>{{ $defaultAddress->city ?? 'City not set' }}, {{ $defaultAddress->state ?? 'State not set' }} {{ $defaultAddress->postal_code ?? '' }}</div>
                                    <div class="font-medium">{{ $defaultAddress->country ?? 'Nigeria' }}</div>
                                </div>
                            </address>
                        </div>
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-map-marker-alt text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">No Default Address Set</h4>
                        <p class="text-gray-500 mb-6">You haven't configured a default shipping address yet.</p>
                        <a href="{{ route('vendor.settings.index') }}#shipping"
                           class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-xl font-medium hover:bg-blue-600 transition-all duration-300 hover:scale-105">
                            <i class="fas fa-plus mr-2"></i>
                            <span>Add Address</span>
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Default Package Sizes -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Default Packages</h3>
                <a href="{{ route('vendor.settings.index') }}#packages" class="text-sm font-medium text-black hover:text-gray-700">Edit</a>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    @forelse($defaultPackages as $pkg)
                        <div class="flex justify-between items-center text-gray-700">
                            <span>{{ $pkg['name'] }} ({{ $pkg['length'] }}x{{ $pkg['width'] }}x{{ $pkg['height'] }} cm)</span>
                            <span class="font-medium">{{ $pkg['weight'] }} kg</span>
                        </div>
                    @empty
                        <p class="text-gray-500">No default package sizes configured.</p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Shipments -->
    <div class="mt-8 bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">Recent Shipments</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full leading-normal">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Tracking #</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Carrier</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($recentShipments as $ship)
                        <tr class="hover:bg-gray-50">
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ $ship->tracking_number }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ strtoupper($ship->carrier) }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ ucfirst($ship->status) }}
                                </span>
                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ $ship->created_at->format('M d, Y') }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-right">
                                <a href="{{ $ship->tracking_url }}" target="_blank" class="text-sm font-medium text-black hover:text-gray-700">Track</a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center py-10 text-gray-500">No shipments yet.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
