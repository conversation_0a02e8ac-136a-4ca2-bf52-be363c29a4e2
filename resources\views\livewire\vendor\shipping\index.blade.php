<div>
    {{-- Close your eyes. Count to one. That is how long forever feels. --}}
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Shipping Overview</h2>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Default Shipping Address -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Default Shipping Address</h3>
                <a href="{{ route('vendor.settings.index') }}#shipping" class="text-sm font-medium text-black hover:text-gray-700">Edit</a>
            </div>
            <div class="p-6">
                @if($defaultAddress)
                    <address class="not-italic text-gray-700">
                        <span class="font-semibold">{{ $defaultAddress->recipient_name }}</span><br>
                        {{ $defaultAddress->line1 }}<br>
                        @if($defaultAddress->line2){{ $defaultAddress->line2 }}<br>@endif
                        {{ $defaultAddress->city }}, {{ $defaultAddress->state }} {{ $defaultAddress->postal_code }}<br>
                        {{ $defaultAddress->country }}
                    </address>
                @else
                    <p class="text-gray-500">You have not set a default shipping address yet.</p>
                @endif
            </div>
        </div>

        <!-- Default Package Sizes -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Default Packages</h3>
                <a href="{{ route('vendor.settings.index') }}#packages" class="text-sm font-medium text-black hover:text-gray-700">Edit</a>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    @forelse($defaultPackages as $pkg)
                        <div class="flex justify-between items-center text-gray-700">
                            <span>{{ $pkg['name'] }} ({{ $pkg['length'] }}x{{ $pkg['width'] }}x{{ $pkg['height'] }} cm)</span>
                            <span class="font-medium">{{ $pkg['weight'] }} kg</span>
                        </div>
                    @empty
                        <p class="text-gray-500">No default package sizes configured.</p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Shipments -->
    <div class="mt-8 bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">Recent Shipments</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full leading-normal">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Tracking #</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Carrier</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($recentShipments as $ship)
                        <tr class="hover:bg-gray-50">
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ $ship->tracking_number }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ strtoupper($ship->carrier) }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ ucfirst($ship->status) }}
                                </span>
                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ $ship->created_at->format('M d, Y') }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-right">
                                <a href="{{ $ship->tracking_url }}" target="_blank" class="text-sm font-medium text-black hover:text-gray-700">Track</a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center py-10 text-gray-500">No shipments yet.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
