<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Commission;
use App\Models\Product;
use App\Services\PaystackService;
use App\Services\ShipBubbleService;
use App\Services\VendorEarningsService;
use App\Models\Vendor;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Throwable;

class PaymentController extends Controller
{
    protected $paystackService;
    protected $shipBubbleService;
    protected $earningsService;

    public function __construct(PaystackService $paystackService, ShipBubbleService $shipBubbleService, VendorEarningsService $earningsService)
    {
        $this->paystackService = $paystackService;
        $this->shipBubbleService = $shipBubbleService;
        $this->earningsService = $earningsService;
    }

    public function initializePaystack(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'street' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'lga' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:255',
            'payment_method' => 'required|string|in:paystack,bank_transfer,cash_on_delivery',
            'shipping_options' => 'required|array',
            'shipping_options.*.courier_id' => 'nullable|string',
            'shipping_options.*.service_code' => 'nullable|string',
            'shipping_options.*.total' => 'required|numeric',
            'shipping_options.*.courier_name' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Validation failed.', 'errors' => $validator->errors()], 422);
        }

        $validatedData = $validator->validated();
        $sessionCart = session('cart', []);

        if (empty($sessionCart)) {
            return response()->json(['message' => 'Your cart is empty.'], 400);
        }

        $productIds = array_keys($sessionCart);
        $products = Product::findMany($productIds)->keyBy('id');

        $cartItems = collect($sessionCart)->map(function ($item, $productId) use ($products) {
            if (!isset($products[$productId])) return null;
            $product = $products[$productId];
            return (object) [
                'id' => $productId,
                'qty' => $item['quantity'],
                'price' => $item['price'],
                'name' => $item['name'],
                'options' => (object) ['vendor_id' => $product->vendor_id],
            ];
        })->filter();

        if ($cartItems->isEmpty()) {
            return response()->json(['message' => 'Your cart is empty or contains invalid products.'], 400);
        }

        // Group cart items by vendor
        $vendorCarts = $cartItems->groupBy('options.vendor_id');
        $shippingOptions = $validatedData['shipping_options'];
        $checkoutTransactionId = 'CHK-'.Str::uuid();
        $createdOrders = [];
        $totalAmount = 0;

        try {
            DB::beginTransaction();

            foreach ($vendorCarts as $vendorId => $items) {
                if (!isset($shippingOptions[$vendorId])) {
                    throw new \Exception("Shipping option for vendor ID {$vendorId} is missing.");
                }

                $shippingRate = $shippingOptions[$vendorId];
                $subtotal = $items->sum(fn($item) => $item->price * $item->qty);
                $shippingCost = $shippingRate['total'];
                $orderTotal = $subtotal + $shippingCost;

                $order = Order::create([
                    'user_id' => auth()->id(),
                    'vendor_id' => $vendorId,
                    'order_number' => 'ORD-' . strtoupper(Str::random(10)),
                    'checkout_transaction_id' => $checkoutTransactionId,
                    'status' => 'pending',
                    'payment_status' => 'pending',
                    'payment_method' => $validatedData['payment_method'],
                    'total' => $orderTotal,
                    'shipping_address' => $validatedData['street'],
                    'shipping_name' => $validatedData['first_name'] . ' ' . $validatedData['last_name'],
                    'shipping_city' => $validatedData['city'],
                    'shipping_lga' => $validatedData['lga'],
                    'shipping_state' => $validatedData['state'],
                    'shipping_postal_code' => $validatedData['postal_code'] ?? null,
                    'shipping_country' => $validatedData['country'],
                    'shipping_phone' => $validatedData['phone'],
                    'shipping_method' => $shippingRate['courier_name'],
                    'shipping_courier_id' => $shippingRate['courier_id'],
                    'shipping_service_code' => $shippingRate['service_code'],
                    'shipping_cost' => $shippingCost,
                    'shipbubble_token' => session('shipbubble_request_tokens.' . $vendorId),
                    'billing_address' => json_encode([
                        'first_name' => $validatedData['first_name'],
                        'last_name' => $validatedData['last_name'],
                        'email' => $validatedData['email'],
                        'phone' => $validatedData['phone'],
                        'address' => $validatedData['street'],
                        'city' => $validatedData['city'],
                        'state' => $validatedData['state'],
                        'postal_code' => $validatedData['postal_code'] ?? null,
                        'country' => $validatedData['country'],
                    ]),
                ]);

                foreach ($items as $cartItem) {
                    $product = Product::find($cartItem->id);
                    $orderItem = $order->items()->create([
                        'product_id' => $cartItem->id,
                        'quantity' => $cartItem->qty,
                        'price' => $cartItem->price,
                        'product_data' => $product ? json_encode($product->toArray()) : null,
                    ]);
                    
                    // Assuming a 10% commission rate
                    $commissionAmount = ($cartItem->price * $cartItem->qty) * 0.10;
                    Commission::create([
                        'vendor_id' => $vendorId,
                        'order_id' => $order->id,
                        'order_item_id' => $orderItem->id,
                        'order_amount' => $cartItem->price * $cartItem->qty,
                        'amount' => $commissionAmount,
                        'status' => 'pending',
                    ]);
                }
                
                $createdOrders[] = $order->id;
                $totalAmount += $orderTotal;
            }

            // If payment method is not Paystack, we can commit and redirect to a generic success page
            if ($validatedData['payment_method'] !== 'paystack') {
                DB::commit();
                Cart::destroy();
                return response()->json([
                    'message' => 'Order placed successfully!',
                    'redirect_url' => route('checkout.success', ['transaction_id' => $checkoutTransactionId])
                ]);
            }

            // Initialize Paystack transaction for the total amount
            $paystackData = [
                'email' => $validatedData['email'],
                'amount' => $totalAmount * 100, // Paystack amount is in kobo
                'reference' => $checkoutTransactionId,
                'callback_url' => route('payment.paystack.callback'),
                'metadata' => [
                    'order_ids' => $createdOrders,
                    'user_id' => auth()->id(),
                    'checkout_transaction_id' => $checkoutTransactionId,
                ]
            ];

            $result = $this->paystackService->initializeTransaction($paystackData);

            if (!$result || !$result['status']) {
                throw new \Exception($result['message'] ?? 'Failed to initialize payment.');
            }
            
            DB::commit();
            
            return response()->json(['redirect_url' => $result['data']['authorization_url']]);

        } catch (Throwable $e) {
            DB::rollBack();
            // Log the detailed error information
            \Illuminate\Support\Facades\Log::error('Paystack Initialization Failed: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            // Return a generic error message to the user
            return response()->json(['message' => 'An error occurred while processing your order. Please try again.'], 500);
        }
    }

    public function handlePaystackCallback(Request $request)
    {
        $reference = $request->input('reference');

        if (!$reference) {
            return redirect()->route('checkout.index')->with('error', 'Payment reference not found.');
        }

        $transaction = $this->paystackService->verifyTransaction($reference);

        if ($transaction && $transaction['status'] && $transaction['data']['status'] === 'success') {
            $metadata = $transaction['data']['metadata'];
            $checkoutTransactionId = $metadata['checkout_transaction_id'] ?? $reference;

            $orders = Order::where('checkout_transaction_id', $checkoutTransactionId)->get();

            foreach ($orders as $order) {
                $order->update([
                    'payment_status' => 'paid',
                    'status' => 'processing',
                    'payment_details' => json_encode($transaction['data']),
                ]);

                // Process vendor earnings
                if ($order->vendor) {
                    $commissionRate = 0.10; // 10% commission
                    $commission = $order->total * $commissionRate;
                    $vendorEarnings = $order->total - $commission;

                    // Credit the vendor for the sale
                    $this->earningsService->creditSale($order->vendor, $order, $vendorEarnings, "Sale from order #{$order->order_number}");

                    // Debit the commission
                    $this->earningsService->debitCommission($order->vendor, $order, $commission, "Commission for order #{$order->order_number}");

                    // Log the commission
                    Commission::create([
                        'order_id' => $order->id,
                        'vendor_id' => $order->vendor_id,
                        'amount' => $commission,
                        'status' => 'completed',
                    ]);
                }

                // After confirming payment, create the shipment via ShipBubble
                if ($order->shipbubble_token && $order->shipping_courier_id && $order->shipping_service_code) {
                    $shipmentData = [
                        'request_token' => $order->shipbubble_token,
                        'courier_id' => $order->shipping_courier_id,
                        'service_code' => $order->shipping_service_code,
                    ];

                    $shipmentResult = $this->shipBubbleService->createShipment($shipmentData);

                    if ($shipmentResult && $shipmentResult['status'] === 'success') {
                        $order->update([
                            'shipping_tracking_url' => $shipmentResult['data']['tracking_url'] ?? null,
                            'shipping_provider_order_id' => $shipmentResult['data']['order_id'] ?? null,
                        ]);
                    } else {
                        Log::error('Failed to create ShipBubble shipment for order ' . $order->id, [
                            'response' => $shipmentResult,
                        ]);
                    }
                }
            }
            
            session()->forget('cart');

            return redirect()->route('checkout.success', ['transaction_id' => $checkoutTransactionId])
                ->with('success', 'Payment successful! Your order is being processed.');
        }

        return redirect()->route('checkout.index')->with('error', 'Payment verification failed.');
    }

    public function checkoutSuccess(Request $request, $transaction_id = null)
    {
        $transactionId = $transaction_id ?? $request->query('transaction_id');
        if (!$transactionId) {
            return redirect()->route('home');
        }

        $orders = Order::where('checkout_transaction_id', $transactionId)
            ->where('user_id', auth()->id())
            ->with('vendor', 'items.product')
            ->get();

        if ($orders->isEmpty()) {
            return redirect()->route('home')->with('error', 'No order found.');
        }

        return view('checkout.success', compact('orders'));
    }
}
