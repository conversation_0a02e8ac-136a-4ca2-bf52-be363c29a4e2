<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('color_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('size_id')->nullable()->constrained()->onDelete('set null');
            $table->string('sku')->unique()->nullable();
            $table->decimal('price_adjustment', 10, 2)->nullable()->comment('Adjustment from base product price. Can be positive or negative.');
            $table->integer('stock_quantity')->default(0);
            $table->string('image_path')->nullable()->comment('Path to variant-specific image.');
            $table->timestamps();

            // Ensure a product cannot have the exact same color and size combination twice
            $table->unique(['product_id', 'color_id', 'size_id'], 'product_color_size_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
