<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\ProductVariant;

class Color extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'hex_code'];

    /**
     * Get the product variants associated with the color.
     */
    public function productVariants()
    {
        return $this->hasMany(ProductVariant::class);
    }
}
