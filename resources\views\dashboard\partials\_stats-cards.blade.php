<!-- Welcome Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-2xl font-bold">Welcome back, {{ auth()->user()->name }}!</h3>
        <p class="text-gray-500">Here's what's happening with your account.</p>
    </div>
    <a href="{{ route('products.index') }}" class="inline-flex items-center px-4 py-2 bg-black text-white border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-gray-800 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
        <i class="fas fa-shopping-cart mr-2"></i> Continue Shopping
    </a>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
    <!-- Total Orders -->
    <div class="bg-white p-6 rounded-lg shadow-md flex items-center">
        <div class="bg-gray-100 rounded-full p-3 mr-4">
            <i class="fas fa-shopping-bag text-2xl text-black"></i>
        </div>
        <div>
            <h6 class="text-gray-500 text-sm">Total Orders</h6>
            <h4 class="font-bold text-2xl">{{ auth()->user()->orders()->count() }}</h4>
        </div>
    </div>

    <!-- Total Spent -->
    <div class="bg-white p-6 rounded-lg shadow-md flex items-center">
        <div class="bg-gray-100 rounded-full p-3 mr-4">
            <i class="fas fa-dollar-sign text-2xl text-black"></i>
        </div>
        <div>
            <h6 class="text-gray-500 text-sm">Total Spent</h6>
            <h4 class="font-bold text-2xl">₦{{ number_format(auth()->user()->orders()->sum('total'), 2) }}</h4>
        </div>
    </div>

    <!-- Wishlist Items -->
    <div class="bg-white p-6 rounded-lg shadow-md flex items-center">
        <div class="bg-gray-100 rounded-full p-3 mr-4">
            <i class="fas fa-heart text-2xl text-black"></i>
        </div>
        <div>
            <h6 class="text-gray-500 text-sm">In Wishlist</h6>
            <h4 class="font-bold text-2xl">{{ auth()->user()->wishlist()->count() }}</h4>
        </div>
    </div>
</div>
