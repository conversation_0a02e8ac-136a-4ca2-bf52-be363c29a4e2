<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Welcome Back, {{ Auth::user()->name }}
                </h1>
                <p class="text-gray-300 text-lg">Manage your store and track your success</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.products.create') }}"
                   class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center space-x-2">
                    <i class="fa-solid fa-plus transition-transform duration-300 group-hover:rotate-90"></i>
                    <span>Add Product</span>
                </a>
                <a href="{{ route('vendor.orders.index') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-shopping-cart transition-transform duration-300 group-hover:bounce"></i>
                    <span>View Orders</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Modern Stats Grid --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {{-- Total Sales Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-money-bill-wave text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full {{ $salesGrowth >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }} transition-all duration-300">
                        {{ $salesGrowth >= 0 ? '↗' : '↘' }} {{ abs($salesGrowth) }}%
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Sales</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">
                    @currency($totalSales)
                </p>
                <p class="text-xs text-gray-400">vs last month</p>
            </div>
        </div>
        {{-- Total Orders Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-shopping-cart text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full {{ $orderGrowth >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }} transition-all duration-300">
                        {{ $orderGrowth >= 0 ? '↗' : '↘' }} {{ abs($orderGrowth) }}%
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Orders</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    {{ number_format($totalOrders) }}
                </p>
                <p class="text-xs text-gray-400">vs last month</p>
            </div>
        </div>
        {{-- Total Products Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-box text-white text-xl"></i>
                </div>
                <div class="text-right">
                    <span class="text-xs font-semibold px-3 py-1 rounded-full bg-blue-100 text-blue-800 transition-all duration-300">
                        +{{ $newProductsThisMonth }} new
                    </span>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Products</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">
                    {{ number_format($totalProducts) }}
                </p>
                <p class="text-xs text-gray-400">this month</p>
            </div>
        </div>
        {{-- Subscription Status Card --}}
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-crown text-white text-xl"></i>
                </div>
                <div class="text-right">
                    @if($daysRemaining > 0)
                        <span class="text-xs font-semibold px-3 py-1 rounded-full bg-green-100 text-green-800">
                            {{ $daysRemaining }}d left
                        </span>
                    @else
                        <span class="text-xs font-semibold px-3 py-1 rounded-full bg-red-100 text-red-800">
                            Expired
                        </span>
                    @endif
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Subscription</p>
                <p class="text-2xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">
                    {{ $subscriptionName }}
                </p>
                @if($daysRemaining <= 0)
                    <a href="{{ route('vendor.subscription.index') }}" class="text-xs text-red-600 hover:text-red-800 font-medium transition-colors duration-300">
                        Renew now →
                    </a>
                @else
                    <p class="text-xs text-gray-400">{{ $daysRemaining }} days remaining</p>
                @endif
            </div>
        </div>
    </div>
            </div>

            {{-- Sales Chart --}}
            <div class="bg-white p-6 rounded-lg shadow-md mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Sales Overview</h3>
                <div wire:ignore>
                    <canvas id="salesChart"></canvas>
                </div>
            </div>

            {{-- Recent Orders --}}
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Recent Orders</h3>
                    <a href="{{ route('vendor.orders.index') }}" class="text-sm font-semibold text-black hover:underline">View All</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3">Order ID</th>
                                <th scope="col" class="px-6 py-3">Customer</th>
                                <th scope="col" class="px-6 py-3">Amount</th>
                                <th scope="col" class="px-6 py-3">Status</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recentOrders as $order)
                                <tr class="bg-white border-b hover:bg-gray-50">
                                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">#{{ $order->id }}</th>
                                    <td class="px-6 py-4">{{ $order->user->name ?? 'Guest' }}</td>
                                    <td class="px-6 py-4">@currency($order->items->sum(fn($item) => $item->price * $item->quantity))</td>
                                    <td class="px-6 py-4">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $order->status_class }}">{{ $order->status }}</span>
                                    </td>
                                    <td class="px-6 py-4">{{ $order->created_at->format('M d, Y') }}</td>
                                    <td class="px-6 py-4">
                                        <a href="{{ route('vendor.orders.show', $order) }}" class="font-medium text-black hover:underline">View</a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-8 text-gray-500">No recent orders found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        {{-- Sidebar --}}
        <div class="lg:col-span-1">
            {{-- Top Products --}}
            <div class="bg-white rounded-lg shadow-md mb-8">
                <div class="p-6 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Top Products</h3>
                    <a href="{{ route('vendor.products.index') }}" class="text-sm font-semibold text-black hover:underline">View All</a>
                </div>
                <div class="divide-y divide-gray-200">
                    @forelse($productPerformance as $product)
                        <div class="p-4 flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="{{ $product->image_url ?? asset('storage/product-placeholder.png') }}" alt="{{ $product->name }}" class="w-10 h-10 rounded-md object-cover mr-4">
                                <div>
                                    <p class="font-semibold text-gray-800 truncate w-40">{{ $product->name }}</p>
                                    <p class="text-sm text-gray-500">@currency($product->price)</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-900">{{ $product->total_sold }}</p>
                                <p class="text-sm text-gray-500">sold</p>
                            </div>
                        </div>
                    @empty
                        <p class="p-6 text-center text-gray-500">No product data available yet.</p>
                    @endforelse
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="bg-white rounded-lg shadow-md p-6">
                 <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                 <div class="space-y-3">
                    <a href="{{ route('vendor.orders.index') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fa-solid fa-shopping-cart mr-3"></i>
                        Manage Orders
                    </a>
                    <a href="{{ route('vendor.settings.index') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fa-solid fa-store-alt mr-3"></i>
                        Shop Settings
                    </a>
                    <a href="{{ route('vendor.subscription.index') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fa-solid fa-crown mr-3"></i>
                        Manage Subscription
                    </a>
                 </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('livewire:load', function () {
        const salesChartCtx = document.getElementById('salesChart').getContext('2d');
        
        // Fetch chart data from a Livewire method
        @this.call('getAnalyticsData').then(response => {
            const salesData = response.sales_over_time;
            const labels = salesData.map(d => d.date);
            const data = salesData.map(d => d.total);

            new Chart(salesChartCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Sales',
                        data: data,
                        borderColor: 'rgba(0, 0, 0, 1)',
                        backgroundColor: 'rgba(0, 0, 0, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        });
    });
</script>
@endpush
