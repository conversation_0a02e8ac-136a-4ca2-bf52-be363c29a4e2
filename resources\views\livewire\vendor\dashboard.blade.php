<div>
    {{-- Page title and actions --}}
    <div class="flex items-center justify-between mb-8">
        <h1 class="text-2xl font-bold text-gray-800">Dashboard</h1>
        <a href="{{ route('vendor.products.create') }}" class="bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors flex items-center">
            <i class="fa-solid fa-plus mr-2"></i>
            Add New Product
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {{-- Main Content --}}
        <div class="lg:col-span-2">
            {{-- Stats Cards --}}
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                {{-- Total Sales --}}
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">Total Sales</p>
                            <p class="text-3xl font-bold text-gray-900">@currency($totalSales)</p>
                        </div>
                        <div class="text-green-500">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <span class="text-xs font-semibold px-2 py-1 rounded-full {{ $salesGrowth >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $salesGrowth >= 0 ? '▲' : '▼' }} {{ abs($salesGrowth) }}%
                        </span>
                        <span class="text-sm text-gray-500 ml-2">from last month</span>
                    </div>
                </div>
                {{-- Total Orders --}}
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">Total Orders</p>
                            <p class="text-3xl font-bold text-gray-900">{{ $totalOrders }}</p>
                        </div>
                        <div class="text-blue-500">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <span class="text-xs font-semibold px-2 py-1 rounded-full {{ $orderGrowth >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $orderGrowth >= 0 ? '▲' : '▼' }} {{ abs($orderGrowth) }}%
                        </span>
                        <span class="text-sm text-gray-500 ml-2">from last month</span>
                    </div>
                </div>
                {{-- Total Products --}}
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">Total Products</p>
                            <p class="text-3xl font-bold text-gray-900">{{ $totalProducts }}</p>
                        </div>
                        <div class="text-indigo-500">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <span class="text-xs font-semibold px-2 py-1 rounded-full bg-green-100 text-green-800">
                            +{{ $newProductsThisMonth }}
                        </span>
                        <span class="text-sm text-gray-500 ml-2">new this month</span>
                    </div>
                </div>
                {{-- Subscription --}}
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">Subscription</p>
                            <p class="text-xl font-bold text-gray-900">{{ $subscriptionName }}</p>
                        </div>
                        <div class="text-yellow-500">
                            <i class="fas fa-crown fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        @if($daysRemaining > 0)
                            <p class="text-sm text-gray-600">{{ $daysRemaining }} days remaining</p>
                        @else
                            <a href="{{ route('vendor.subscription.index') }}" class="text-sm text-red-600 hover:underline">Renew now</a>
                        @endif
                    </div>
                </div>
            </div>

            {{-- Sales Chart --}}
            <div class="bg-white p-6 rounded-lg shadow-md mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Sales Overview</h3>
                <div wire:ignore>
                    <canvas id="salesChart"></canvas>
                </div>
            </div>

            {{-- Recent Orders --}}
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Recent Orders</h3>
                    <a href="{{ route('vendor.orders.index') }}" class="text-sm font-semibold text-black hover:underline">View All</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3">Order ID</th>
                                <th scope="col" class="px-6 py-3">Customer</th>
                                <th scope="col" class="px-6 py-3">Amount</th>
                                <th scope="col" class="px-6 py-3">Status</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recentOrders as $order)
                                <tr class="bg-white border-b hover:bg-gray-50">
                                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">#{{ $order->id }}</th>
                                    <td class="px-6 py-4">{{ $order->user->name ?? 'Guest' }}</td>
                                    <td class="px-6 py-4">@currency($order->items->sum(fn($item) => $item->price * $item->quantity))</td>
                                    <td class="px-6 py-4">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $order->status_class }}">{{ $order->status }}</span>
                                    </td>
                                    <td class="px-6 py-4">{{ $order->created_at->format('M d, Y') }}</td>
                                    <td class="px-6 py-4">
                                        <a href="{{ route('vendor.orders.show', $order) }}" class="font-medium text-black hover:underline">View</a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-8 text-gray-500">No recent orders found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        {{-- Sidebar --}}
        <div class="lg:col-span-1">
            {{-- Top Products --}}
            <div class="bg-white rounded-lg shadow-md mb-8">
                <div class="p-6 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Top Products</h3>
                    <a href="{{ route('vendor.products.index') }}" class="text-sm font-semibold text-black hover:underline">View All</a>
                </div>
                <div class="divide-y divide-gray-200">
                    @forelse($productPerformance as $product)
                        <div class="p-4 flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="{{ $product->image_url ?? asset('storage/product-placeholder.png') }}" alt="{{ $product->name }}" class="w-10 h-10 rounded-md object-cover mr-4">
                                <div>
                                    <p class="font-semibold text-gray-800 truncate w-40">{{ $product->name }}</p>
                                    <p class="text-sm text-gray-500">@currency($product->price)</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-900">{{ $product->total_sold }}</p>
                                <p class="text-sm text-gray-500">sold</p>
                            </div>
                        </div>
                    @empty
                        <p class="p-6 text-center text-gray-500">No product data available yet.</p>
                    @endforelse
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="bg-white rounded-lg shadow-md p-6">
                 <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                 <div class="space-y-3">
                    <a href="{{ route('vendor.orders.index') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fa-solid fa-shopping-cart mr-3"></i>
                        Manage Orders
                    </a>
                    <a href="{{ route('vendor.settings.index') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fa-solid fa-store-alt mr-3"></i>
                        Shop Settings
                    </a>
                    <a href="{{ route('vendor.subscription.index') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fa-solid fa-crown mr-3"></i>
                        Manage Subscription
                    </a>
                 </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('livewire:load', function () {
        const salesChartCtx = document.getElementById('salesChart').getContext('2d');
        
        // Fetch chart data from a Livewire method
        @this.call('getAnalyticsData').then(response => {
            const salesData = response.sales_over_time;
            const labels = salesData.map(d => d.date);
            const data = salesData.map(d => d.total);

            new Chart(salesChartCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Sales',
                        data: data,
                        borderColor: 'rgba(0, 0, 0, 1)',
                        backgroundColor: 'rgba(0, 0, 0, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        });
    });
</script>
@endpush
