<?php

use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use App\Livewire\Vendor\Dashboard as VendorDashboard;
use App\Livewire\Vendor\Orders\Index as VendorOrdersIndex;
use App\Livewire\Vendor\Orders\Show as VendorOrdersShow;
use App\Livewire\Vendor\Shipping\Index as VendorShippingIndex;
use App\Livewire\Vendor\Earnings\Index as VendorEarningsIndex;
use App\Livewire\Vendor\Profile\Index as VendorProfileIndex;
use App\Livewire\Vendor\Subscription\Index as VendorSubscriptionIndex;
use App\Livewire\Vendor\Settings\Index as VendorSettingsIndex;
use App\Livewire\Vendor\Reviews\Index as VendorReviewsIndex;
use App\Livewire\Vendor\Pending\Index as VendorPendingIndex;
use App\Livewire\Vendor\Onboarding\Index as VendorOnboardingIndex;
use App\Livewire\Vendor\Products\Index as VendorProductsIndex;
use App\Livewire\Vendor\Products\ProductForm;
use App\Livewire\Auth\Vendor\Register;
use App\Livewire\Admin\Products\Index as AdminProductsIndex;
use App\Livewire\Admin\Products\ProductForm as AdminProductForm;
use App\Livewire\Admin\Vendors\Index as AdminVendorsIndex;
use App\Livewire\Admin\Vendors\VendorForm as AdminVendorForm;
use App\Livewire\Admin\Users\Index as AdminUsersIndex;
use App\Livewire\Admin\Users\UserForm as AdminUserForm;
use App\Livewire\Admin\Commissions\Index as AdminCommissionsIndex;
use App\Livewire\Admin\Withdrawals\Index as AdminWithdrawalsIndex;
use App\Livewire\Admin\Subscriptions\Index as AdminSubscriptionsIndex;
use App\Livewire\Admin\SubscriptionPlans\Index as AdminSubscriptionPlansIndex;
use App\Livewire\Admin\Payments\Index as AdminPaymentsIndex;
use App\Livewire\Admin\Brands\Index as AdminBrandsIndex;
use App\Livewire\Admin\Categories\Index as AdminCategoriesIndex;
use App\Livewire\Admin\Orders\Index as AdminOrdersIndex;
use App\Livewire\Admin\Orders\Show as AdminOrdersShow;
use App\Livewire\Cart\Index;
use Illuminate\Support\Facades\Route;

// Public Routes
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home'); // Uses dynamic welcome-bw.blade.php

// Static Pages
Route::view('/about', 'pages.about')->name('about');
Route::view('/contact', 'pages.contact')->name('contact');
Route::post('/contact/submit', [App\Http\Controllers\HomeController::class, 'handleContactForm'])->name('contact.submit');

// Products Routes
Route::get('/shop', \App\Livewire\Product\Index::class)->name('products.index');
Route::get('/category/{category:slug}', \App\Livewire\Product\Category::class)->name('products.category');
Route::get('/products/{product:slug}', \App\Livewire\Product\Show::class)->name('products.show');
Route::get('/search', \App\Livewire\Product\Search::class)->name('products.search');
Route::post('/product/{product:slug}/reviews', [App\Http\Controllers\ReviewController::class, 'store'])->name('products.reviews.store')->middleware('auth');

// Vendor Storefront
Route::get('/vendors/{vendor:slug}', \App\Livewire\Vendor\Storefront::class)->name('vendors.storefront');

// Cart Routes
Route::get('/cart', Index::class)->name('cart.index');
Route::post('/cart/add/{product}', [\App\Http\Controllers\CartController::class, 'add'])->name('cart.add');


// Vendor Registration and Onboarding
Route::get('/become-a-vendor', \App\Livewire\Auth\Vendor\Register::class)->name('vendor.register');

Route::get('/store/{slug}', [App\Http\Controllers\VendorController::class, 'show'])->name('vendor.show');

// Customer Routes
Route::middleware(['auth', 'verified'])->group(function () {
        Route::get('/dashboard', \App\Livewire\Dashboard\Index::class)->name('dashboard');

    Route::redirect('settings', 'settings/profile');
    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');

    // Wishlist Routes
    Route::get('/wishlist', \App\Livewire\Wishlist\Index::class)->name('wishlist.index');
    Route::post('/wishlist/add/{product}', [App\Http\Controllers\WishlistController::class, 'add'])->name('wishlist.add');
    Route::delete('/wishlist/remove/{product}', [App\Http\Controllers\WishlistController::class, 'remove'])->name('wishlist.remove');
    Route::post('/wishlist/clear', [App\Http\Controllers\WishlistController::class, 'clear'])->name('wishlist.clear');

    // Order Routes
    Route::get('/orders', \App\Livewire\Orders\Index::class)->name('orders.index');
    Route::get('/orders/{order}', [App\Http\Controllers\OrderController::class, 'show'])->name('orders.show');
    Route::post('/orders/{order}/cancel', [App\Http\Controllers\OrderController::class, 'cancel'])->name('orders.cancel');
    Route::post('/orders/{order}/return', [App\Http\Controllers\OrderController::class, 'requestReturn'])->name('orders.return');

    // Checkout Routes
        Route::get('/checkout', \App\Livewire\Checkout\Index::class)->name('checkout.index');

    // Shipping Routes
    Route::post('/shipping/rates', [\App\Http\Controllers\ShippingController::class, 'getRates'])->name('shipping.rates');

    // Payment Routes
    Route::post('/payment/paystack/initialize', [\App\Http\Controllers\PaymentController::class, 'initializePaystack'])->name('payment.paystack.initialize');
    Route::get('/payment/paystack/callback', [\App\Http\Controllers\PaymentController::class, 'handlePaystackCallback'])->name('payment.paystack.callback');
        Route::get('/checkout/success/{transaction_id?}', \App\Livewire\Checkout\Success::class)->name('checkout.success');
});

// Vendor Routes
Route::middleware(['auth', 'vendor'])->prefix('vendor')->name('vendor.')->group(function () {
    // Dashboard
    Route::get('/dashboard', VendorDashboard::class)->name('dashboard');

    // Onboarding Wizard
    Route::get('/onboarding', VendorOnboardingIndex::class)->name('onboarding');

    
    // Products Management
    Route::get('/products', VendorProductsIndex::class)->name('products.index');
    Route::get('products/create', ProductForm::class)->name('products.create');
    Route::get('products/{product}/edit', ProductForm::class)->name('products.edit');
    Route::resource('products', \App\Http\Controllers\Vendor\ProductController::class)->only(['destroy']);
    
    // Orders Management
    Route::get('/orders', VendorOrdersIndex::class)->name('orders.index');
    Route::get('/orders/{order}', VendorOrdersShow::class)->name('orders.show');

    
    // Shipping
        Route::get('/shipping', VendorShippingIndex::class)->name('shipping.index');

    // Earnings & Commissions
    Route::get('/earnings', VendorEarningsIndex::class)->name('earnings.index');
    
    // Profile Management
    Route::get('/profile', VendorProfileIndex::class)->name('profile');
    
    // Subscription Management
    Route::get('/subscription', VendorSubscriptionIndex::class)->name('subscription.index');
    Route::get('/subscription/callback', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'handleCallback'])->name('subscription.callback');
    
    // Reviews
    Route::get('/reviews', VendorReviewsIndex::class)->name('reviews.index');

    // Profile & Settings
    Route::get('/settings', VendorSettingsIndex::class)->name('settings.index');
    

    
    // Pending Approval
    Route::get('/pending', VendorPendingIndex::class)->name('pending')->withoutMiddleware('approved.vendor');
});

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    
    // User Management
    Route::get('users', AdminUsersIndex::class)->name('users.index');
    Route::get('users/create', AdminUserForm::class)->name('users.create');
    Route::get('users/{user}/edit', AdminUserForm::class)->name('users.edit');
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class)->only(['destroy']);
    
    // Vendor Management
    Route::get('vendors', AdminVendorsIndex::class)->name('vendors.index');
    Route::get('vendors/create', AdminVendorForm::class)->name('vendors.create');
    Route::get('vendors/{vendor}/edit', AdminVendorForm::class)->name('vendors.edit');
    Route::resource('vendors', \App\Http\Controllers\Admin\VendorController::class)->only(['destroy']);
    
    // Product Management
    Route::get('products', AdminProductsIndex::class)->name('products.index');
    Route::get('products/create', AdminProductForm::class)->name('products.create');
    Route::get('products/{product}/edit', AdminProductForm::class)->name('products.edit');
    
    // Category Management
    Route::get('categories', AdminCategoriesIndex::class)->name('categories.index');
    
    // Brand Management
    Route::get('brands', AdminBrandsIndex::class)->name('brands.index');
    
    // Order Management
    Route::get('orders', AdminOrdersIndex::class)->name('orders.index');
    Route::get('orders/{order}', AdminOrdersShow::class)->name('orders.show');
    
    // Commission Management
    Route::get('commissions', AdminCommissionsIndex::class)->name('commissions.index');
    
    // Subscription Management
    Route::get('subscriptions', AdminSubscriptionsIndex::class)->name('subscriptions.index');
    
    // Subscription Plan Management
    Route::get('subscription-plans', AdminSubscriptionPlansIndex::class)->name('subscription-plans.index');
    
    // Payment Management
    Route::get('payments', AdminPaymentsIndex::class)->name('payments.index');

    // Withdrawal Management
    Route::get('withdrawals', AdminWithdrawalsIndex::class)->name('withdrawals.index');
    
    // Settings
    Route::get('/settings', \App\Livewire\Admin\Settings\Index::class)->name('admin.settings.index');
    
    // Profile
    Route::get('/profile', function () {
        return view('admin.profile');
    })->name('profile');
});

// Paystack Webhook
Route::post('/paystack/webhook', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'handleWebhook'])->name('paystack.webhook');

require __DIR__.'/auth.php';
