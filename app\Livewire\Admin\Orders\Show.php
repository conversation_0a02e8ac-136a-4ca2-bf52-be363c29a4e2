<?php

namespace App\Livewire\Admin\Orders;

use App\Models\Order;
use Livewire\Component;
use Livewire\Attributes\Layout;
use WireUi\Traits\WireUiActions;

#[Layout('layouts.admin')]
class Show extends Component
{
    use WireUiActions;

    public Order $order;
    public string $status;

    public function mount(Order $order)
    {
        $this->order = $order->load(['user', 'items.product.featured_image', 'shippingAddress']);
        $this->status = $this->order->status;
    }

    public function updateStatus()
    {
        $this->validate(['status' => 'required|in:pending,processing,shipped,delivered,cancelled']);

        $this->order->update(['status' => $this->status]);

        // Optionally, send a notification to the user
        // Mail::to($this->order->user)->send(new OrderStatusUpdated($this->order));

        $this->notification()->success('Order status updated successfully.');
    }

    public function render()
    {
        return view('livewire.admin.orders.show');
    }
}
