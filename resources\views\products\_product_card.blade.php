<div class="group relative flex h-full flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800">
    <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200">
        <a href="{{ route('products.show', $product->slug) }}">
            <img src="{{ $product->image_url ?? asset('images/default-product.png') }}" alt="{{ $product->name }}" class="h-full w-full object-cover object-center transition-transform duration-300 group-hover:scale-105">
        </a>
    </div>
    <div class="flex flex-1 flex-col space-y-2 p-4">
        <div class="flex-1">
            <p class="text-xs text-gray-500 dark:text-gray-400">
                <a href="{{ route('products.category', $product->category->slug) }}" class="hover:underline">{{ $product->category->name ?? 'N/A' }}</a>
            </p>
            <h3 class="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                <a href="{{ route('products.show', $product->slug) }}">
                    <span aria-hidden="true" class="absolute inset-0"></span>
                    {{ Str::limit($product->name, 50) }}
                </a>
            </h3>
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Sold by: <a href="{{ route('vendors.storefront', $product->vendor->slug) }}" class="font-medium text-black hover:underline dark:text-white">{{ $product->vendor->shop_name ?? 'N/A' }}</a>
            </p>
        </div>
        <div class="flex items-end justify-between pt-2">
            <div>
                @if($product->is_on_sale)
                    <p class="text-lg font-bold text-red-600">₦{{ number_format($product->discount_price, 2) }}</p>
                    <p class="text-sm text-gray-500 line-through">₦{{ number_format($product->price, 2) }}</p>
                @else
                    <p class="text-lg font-bold text-gray-900 dark:text-white">₦{{ number_format($product->price, 2) }}</p>
                @endif
            </div>

            <div class="relative z-10">
                @if($product->is_active)
                    <form action="{{ route('cart.add', $product->id) }}" method="POST" class="ajax-add-to-cart-form">
                        @csrf
                        <input type="hidden" name="quantity" value="1">
                        <button type="submit" class="add-to-cart-btn flex items-center justify-center rounded-md border border-transparent bg-black px-3 py-2 text-sm font-medium text-white transition-colors hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 dark:bg-white dark:text-black dark:hover:bg-gray-200 dark:focus:ring-white">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </form>
                @else
                    <button class="flex items-center justify-center rounded-md border border-gray-300 bg-gray-200 px-3 py-2 text-sm font-medium text-gray-500 cursor-not-allowed" disabled>
                        Unavailable
                    </button>
                @endif
            </div>
        </div>
    </div>
</div>
