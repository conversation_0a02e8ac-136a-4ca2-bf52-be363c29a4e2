<?php

namespace App\Livewire\Admin\Vendors;

use App\Models\Vendor;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortBy = 'created_at';
    public string $sortDirection = 'desc';

    public function approve(Vendor $vendor): void
    {
        $vendor->update(['approved_at' => now()]);
        session()->flash('success', 'Vendor approved successfully.');
    }

    public function reject(Vendor $vendor): void
    {
        $vendor->update(['approved_at' => null]);
        session()->flash('success', 'Vendor rejected successfully.');
    }

    public function toggleFeatured(Vendor $vendor): void
    {
        $vendor->update(['is_featured' => !$vendor->is_featured]);
        session()->flash('success', 'Vendor featured status updated successfully.');
    }

    public function sortBy(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortBy = $field;
    }

    public function render()
    {
        $vendors = Vendor::query()
            ->with('user')
            ->when($this->search, function ($query) {
                $query->where('shop_name', 'like', '%' . $this->search . '%')
                    ->orWhereHas('user', function ($query) {
                        $query->where('email', 'like', '%' . $this->search . '%');
                    });
            })
            ->orderBy($this->sortBy, $this->sortDirection)
            ->paginate(15);

        return view('livewire.admin.vendors.index', [
            'vendors' => $vendors,
        ])->layout('layouts.admin');
    }
}
