<?php

namespace App\Livewire\Vendor\Onboarding;

use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Index extends Component
{
    use WithFileUploads;

    public Vendor $vendor;
    public string $step = 'business';

    // Step 1: Business Information
    public $business_name;
    public $phone;
    public $business_address;
    public $business_description;

    // Step 2: Documents
    public $id_document;
    public $business_document;

    // Step 3: Payout Information
    public $bank_name;
    public $bank_account_name;
    public $bank_account_number;

    public function mount()
    {
        $this->vendor = Auth::user()->vendor;

        if ($this->vendor->has_completed_onboarding) {
            return redirect()->route('vendor.dashboard');
        }
        
        // Pre-fill properties from vendor model
        $this->business_name = $this->vendor->business_name;
        $this->phone = $this->vendor->phone;
        $this->business_address = $this->vendor->business_address;
        $this->business_description = $this->vendor->business_description;
        $this->bank_name = $this->vendor->bank_name;
        $this->bank_account_name = $this->vendor->bank_account_name;
        $this->bank_account_number = $this->vendor->bank_account_number;

        // Determine the current step
        if ($this->vendor->phone && $this->vendor->business_address) {
            $this->step = 'documents';
        }
        if ($this->vendor->id_document) {
            $this->step = 'payouts';
        }
    }

    public function saveBusinessInfo()
    {
        $this->validate([
            'business_name' => 'required|string|max:255|unique:vendors,business_name,' . $this->vendor->id,
            'phone' => 'required|string|max:20',
            'business_address' => 'required|string|max:500',
            'business_description' => 'required|string|max:1000',
        ]);

        $this->vendor->update([
            'business_name' => $this->business_name,
            'slug' => Str::slug($this->business_name),
            'phone' => $this->phone,
            'business_address' => $this->business_address,
            'business_description' => $this->business_description,
        ]);

        $this->step = 'documents';
        session()->flash('success', 'Business information saved!');
    }

    public function saveDocuments()
    {
        // Remove all file upload fields and validation
    }

    public function savePayoutInfo()
    {
        $this->validate([
            'bank_name' => 'required|string|max:255',
            'bank_account_name' => 'required|string|max:255',
            'bank_account_number' => 'required|string|max:20',
        ]);
        
        $this->vendor->update([
            'bank_name' => $this->bank_name,
            'bank_account_name' => $this->bank_account_name,
            'bank_account_number' => $this->bank_account_number,
            'has_completed_onboarding' => true,
        ]);
        
        return redirect()->route('vendor.dashboard')->with('success', 'Your vendor profile has been completed! Your application is now pending approval.');
    }

    public function render()
    {
        return view('livewire.vendor.onboarding.index');
    }
}
