<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add order_number field if it doesn't exist
            if (!Schema::hasColumn('orders', 'order_number')) {
                $table->string('order_number')->nullable()->after('id');
            }
            
            // Add shipping details
            $table->string('shipping_name')->nullable()->after('billing_address');
            $table->string('shipping_city')->nullable()->after('shipping_name');
            $table->string('shipping_state')->nullable()->after('shipping_city');
            $table->string('shipping_postal_code')->nullable()->after('shipping_state');
            $table->string('shipping_country')->nullable()->after('shipping_postal_code');
            $table->string('shipping_phone')->nullable()->after('shipping_country');
            $table->string('shipping_method')->nullable()->after('shipping_phone');
            
            // Add payment method
            $table->string('payment_method')->nullable()->after('payment_status');
            
            // Add tracking timestamps
            $table->timestamp('shipped_at')->nullable()->after('shipping_method');
            $table->timestamp('delivered_at')->nullable()->after('shipped_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'shipping_name', 
                'shipping_city', 
                'shipping_state', 
                'shipping_postal_code', 
                'shipping_country', 
                'shipping_phone', 
                'shipping_method',
                'payment_method',
                'shipped_at',
                'delivered_at'
            ]);
        });
    }
};
