<div>
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <img class="mx-auto h-12 w-auto" src="<?php echo e(asset('brandify.png')); ?>" alt="Brandify">
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white"><?php echo e(__('Log in to your account')); ?></h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
            <?php echo e(__('Or')); ?>

            <!--[if BLOCK]><![endif]--><?php if(Route::has('register')): ?>
                <a href="<?php echo e(route('register')); ?>" class="font-medium text-black hover:text-gray-800 dark:text-white dark:hover:text-gray-200" wire:navigate>
                    <?php echo e(__('create a new account')); ?>

                </a>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <!-- Session Status -->
            <!--[if BLOCK]><![endif]--><?php if(session('status')): ?>
                <div class="mb-4 rounded-md bg-green-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800"><?php echo e(session('status')); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <form wire:submit="login" class="space-y-6" wire:loading.class="opacity-75">
                <div class="space-y-2">
                    <label for="email" class="block text-sm font-bold text-gray-900 mb-2">
                        <i class="fas fa-envelope mr-2 text-gray-500"></i><?php echo e(__('Email address')); ?>

                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-at text-gray-400"></i>
                        </div>
                        <input wire:model.live.debounce.300ms="email"
                               id="email"
                               name="email"
                               type="email"
                               autocomplete="email"
                               placeholder="Enter your email address"
                               required
                               class="block w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-0 transition-all duration-300 bg-white <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                            </p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>

                <div class="space-y-2">
                    <label for="password" class="block text-sm font-bold text-gray-900 mb-2">
                        <i class="fas fa-lock mr-2 text-gray-500"></i><?php echo e(__('Password')); ?>

                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-key text-gray-400"></i>
                        </div>
                        <input wire:model.live.debounce.300ms="password"
                               id="password"
                               name="password"
                               type="password"
                               autocomplete="current-password"
                               placeholder="Enter your password"
                               required
                               class="block w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-0 transition-all duration-300 bg-white <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                            </p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input wire:model="remember"
                               id="remember"
                               name="remember"
                               type="checkbox"
                               class="w-5 h-5 text-black border-2 border-gray-300 rounded focus:ring-black focus:ring-2 transition-all duration-300">
                        <label for="remember" class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-200">
                            <?php echo e(__('Remember me')); ?>

                        </label>
                    </div>

                    <!--[if BLOCK]><![endif]--><?php if(Route::has('password.request')): ?>
                        <div class="text-sm">
                            <a href="<?php echo e(route('password.request')); ?>"
                               class="font-medium text-black hover:text-gray-800 dark:text-white dark:hover:text-gray-200 transition-colors duration-300"
                               wire:navigate>
                                <?php echo e(__('Forgot your password?')); ?>

                            </a>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <div>
                    <button type="submit"
                            wire:loading.attr="disabled"
                            class="bg-black text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-800 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 w-full">
                        <div wire:loading wire:target="login" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        <i class="fas fa-sign-in-alt transition-transform duration-300 group-hover:translate-x-1" wire:loading.remove wire:target="login"></i>
                        <span wire:loading.remove wire:target="login"><?php echo e(__('Log in')); ?></span>
                        <span wire:loading wire:target="login"><?php echo e(__('Logging in...')); ?></span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/auth/login.blade.php ENDPATH**/ ?>