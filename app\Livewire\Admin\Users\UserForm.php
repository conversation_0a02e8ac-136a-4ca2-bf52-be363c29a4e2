<?php

namespace App\Livewire\Admin\Users;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use Spatie\Permission\Models\Role;

class UserForm extends Component
{
    public ?User $user = null;

    // Form fields
    public string $name = '';
    public string $email = '';
    public ?string $password = null;
    public ?string $password_confirmation = null;
    public array $selectedRoles = [];

    public $allRoles;

    public function mount(?User $user = null): void
    {
        $this->user = $user;
        $this->allRoles = Role::pluck('name', 'id');

        if ($this->user && $this->user->exists) {
            $this->name = $this->user->name;
            $this->email = $this->user->email;
            $this->selectedRoles = $this->user->roles->pluck('id')->toArray();
        }
    }

    protected function rules(): array
    {
        $userId = $this->user?->id ?? 'NULL';
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $userId,
            'selectedRoles' => 'required|array|min:1',
            'selectedRoles.*' => 'exists:roles,id',
        ];

        if (!$this->user || !$this->user->exists) {
            $rules['password'] = 'required|string|min:8|confirmed';
        } else {
            $rules['password'] = 'nullable|string|min:8|confirmed';
        }

        return $rules;
    }

    public function save()
    {
        $this->validate();

        DB::transaction(function () {
            $data = [
                'name' => $this->name,
                'email' => $this->email,
            ];

            if ($this->password) {
                $data['password'] = Hash::make($this->password);
            }

            if ($this->user && $this->user->exists) {
                $this->user->update($data);
                session()->flash('success', 'User updated successfully.');
            } else {
                $this->user = User::create($data);
                session()->flash('success', 'User created successfully.');
            }

            $this->user->syncRoles($this->selectedRoles);
        });

        return redirect()->route('admin.users.index');
    }

    public function render()
    {
        return view('livewire.admin.users.user-form')
            ->layout('layouts.admin');
    }
}
