<div class="flex flex-col">
    <div class="relative w-full bg-gray-200 rounded-lg overflow-hidden">
        <img src="{{ $selectedImage }}" alt="{{ $product->name }}" class="w-full h-full object-center object-cover aspect-square">
        @if($product->is_on_sale)
            <div class="absolute top-0 left-0 m-4 px-2 py-1 bg-black text-white text-xs font-semibold rounded-md">SALE</div>
        @endif
    </div>
    <div class="mt-4">
        <div class="grid grid-cols-5 gap-2">
            @foreach($images as $image)
                <div wire:click="selectImage('{{ $image }}')" 
                     class="cursor-pointer rounded-lg overflow-hidden border-2 {{ $selectedImage === $image ? 'border-black' : 'border-transparent' }} hover:border-gray-400">
                    <img src="{{ $image }}" alt="{{ $product->name }} thumbnail" class="w-full h-full object-center object-cover aspect-square">
                </div>
            @endforeach
        </div>
    </div>
</div>
{{-- To attain knowledge, add things every day; To attain wisdom, subtract things every day. --}}
