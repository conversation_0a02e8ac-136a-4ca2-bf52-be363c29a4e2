<?php

namespace App\Livewire\Auth\Vendor;

use App\Models\Role;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.app')]
class Register extends Component
{
    public string $name = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';

    public function register()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $vendorRole = Role::where('name', 'vendor')->first();
        if (!$vendorRole) {
            session()->flash('error', 'An internal error occurred. Please contact support.');
            return;
        }

        $user = User::create([
            'name' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password),
            'role_id' => $vendorRole->id,
        ]);

        Vendor::create([
            'user_id' => $user->id,
            'business_name' => $this->name . "'s Store",
            'slug' => Str::slug($this->name . "'s Store"),
            'is_approved' => false,
            'status' => 'pending',
            'has_completed_onboarding' => false,
        ]);

        Auth::login($user);

        return redirect()->route('vendor.onboarding');
    }

    public function render()
    {
        return view('livewire.auth.vendor.register');
    }
}
