<?php

namespace App\Livewire\Vendor\Shipping;

use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.vendor')]
class Index extends Component
{
    public $recentShipments = [];
    public $defaultPackages = [];
    public $defaultAddress;

    public function mount()
    {
        $vendor = Auth::user()->vendor;
        $this->defaultAddress = $vendor->default_shipping_address ?? null;
        // Placeholder data as in the original controller
        $this->recentShipments = []; 
        $this->defaultPackages = []; 
    }

    public function render()
    {
        return view('livewire.vendor.shipping.index');
    }
}
