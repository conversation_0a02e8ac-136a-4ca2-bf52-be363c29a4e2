<?php

namespace App\Livewire\Admin\Commissions;

use App\Models\Commission;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public function updateStatus(int $commissionId, string $status)
    {
        $commission = Commission::findOrFail($commissionId);

        if (!in_array($status, ['pending', 'paid'])) {
            session()->flash('error', 'Invalid status selected.');
            return;
        }

        $commission->status = $status;
        $commission->save();

        session()->flash('success', 'Commission status updated successfully.');
    }

    public function render()
    {
        $commissions = Commission::with('vendor')->latest()->paginate(15);

        return view('livewire.admin.commissions.index', [
            'commissions' => $commissions,
        ]);
    }
}
