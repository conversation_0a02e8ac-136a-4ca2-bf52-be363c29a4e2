<div>
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Subscription Management</h1>
    </div>

    @if (session()->has('success'))
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
            <p>{{ session('success') }}</p>
        </div>
    @endif
    @if (session()->has('error'))
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <p>{{ session('error') }}</p>
        </div>
    @endif

    <!-- Current Subscription -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 class="text-xl font-semibold mb-4">Your Current Plan</h2>
        @if ($subscription && $subscription->status === 'active')
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-lg font-bold text-gray-900">{{ $subscription->plan->name }}</p>
                    <p class="text-gray-600">Status: <span class="font-semibold text-green-600 capitalize">{{ $subscription->status }}</span></p>
                    <p class="text-gray-600">Renews/Expires on: {{ $subscription->ends_at->format('F d, Y') }}</p>
                </div>
                <div>
                    <button wire:click="cancel" wire:confirm="Are you sure you want to cancel your subscription? This action cannot be undone." class="bg-red-500 text-white font-bold py-2 px-4 rounded hover:bg-red-600 transition duration-300">
                        Cancel Subscription
                    </button>
                </div>
            </div>
        @elseif($subscription)
             <p class="text-gray-600">Your {{ $subscription->plan->name }} plan is currently <span class="font-semibold text-yellow-600">{{$subscription->status}}</span>.</p>
        @else
            <p class="text-gray-600">You do not have an active subscription. Choose a plan below to get started.</p>
        @endif
    </div>

    <!-- Available Plans -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        @foreach ($plans as $plan)
            <div class="bg-white rounded-lg shadow-lg p-6 flex flex-col {{ $subscription && $subscription->plan->id == $plan->id && $subscription->status == 'active' ? 'border-2 border-black' : '' }}">
                <h3 class="text-2xl font-bold text-center mb-2">{{ $plan->name }}</h3>
                <p class="text-center text-gray-500 mb-4">{{ $plan->description }}</p>
                <div class="text-center my-4">
                    <span class="text-4xl font-extrabold">₦{{ number_format($plan->price) }}</span>
                    <span class="text-gray-500">/ {{ $plan->duration_days }} days</span>
                </div>
                <ul class="space-y-3 text-gray-600 mb-6 flex-grow">
                    @foreach ($plan->features as $feature)
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                            <span>{{ $feature }}</span>
                        </li>
                    @endforeach
                </ul>
                @if ($subscription && $subscription->plan->id == $plan->id && $subscription->status == 'active')
                    <button disabled class="w-full bg-gray-400 text-white font-bold py-3 px-4 rounded mt-auto cursor-not-allowed">
                        Current Plan
                    </button>
                @else
                    <button wire:click="subscribe({{ $plan->id }})" wire:loading.attr="disabled" class="w-full bg-black text-white font-bold py-3 px-4 rounded hover:bg-gray-800 transition duration-300 mt-auto">
                        <span wire:loading.remove wire:target="subscribe({{ $plan->id }})">Choose Plan</span>
                        <span wire:loading wire:target="subscribe({{ $plan->id }})">Processing...</span>
                    </button>
                @endif
            </div>
        @endforeach
    </div>
</div>
