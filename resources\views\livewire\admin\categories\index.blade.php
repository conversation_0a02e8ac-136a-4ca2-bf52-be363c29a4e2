<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Manage Categories</h1>
            <x-button wire:click="create" label="Add New Category" black />
        </div>

        <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Slug</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Parent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse ($categories as $category)
                            @include('livewire.admin.categories.category-row', ['category' => $category, 'level' => 0])
                        @empty
                            <tr><td colspan="4" class="text-center py-12">No categories found.</td></tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <x-modal.card title="{{ $category?->exists ? 'Edit' : 'Create' }} Category" wire:model.defer="showModal">
        <div class="space-y-4">
            <x-input label="Name" wire:model.lazy="name" />
            <x-input label="Slug" wire:model.defer="slug" />
            <x-select
                label="Parent Category"
                wire:model.defer="parent_id"
                placeholder="Select a parent category"
                :options="$allCategories"
                option-label="name"
                option-value="id"
                clearable
            />
        </div>
        <x-slot name="footer">
            <div class="flex justify-end gap-x-4">
                <x-button flat label="Cancel" x-on:click="close" />
                <x-button black label="Save" wire:click="save" />
            </div>
        </x-slot>
    </x-modal.card>
</div>
