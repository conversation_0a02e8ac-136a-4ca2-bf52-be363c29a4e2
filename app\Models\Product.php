<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Support\Facades\Storage;

class Product extends Model implements HasMedia
{
    use HasFactory, HasSlug, SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'vendor_id',
        'category_id',
        'brand_id',
        'name',
        'slug',
        'description',
        'price',
        'discount_price',
        'image_url',
        'weight',
        'height',
        'width',
        'length',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    
    public function wishlists()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function wishlistedBy()
    {
        return $this->belongsToMany(User::class, 'wishlists');
    }

    public function getCurrentPrice()
    {
        return $this->discount_price > 0 ? $this->discount_price : $this->price;
    }
    
    public function getDiscountPercentage()
    {
        if (!$this->discount_price || $this->discount_price <= 0 || $this->price <= 0) {
            return 0;
        }
        
        return round((($this->price - $this->discount_price) / $this->price) * 100);
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    /**
     * Check if product is on sale
     *
     * @return bool
     */
    public function isOnSale()
    {
        return $this->discount_price > 0 && $this->discount_price < $this->price;
    }

    public function averageRating()
    {
        return $this->reviews()->avg('rating');
    }

    public function reviewCount()
    {
        return $this->reviews()->count();
    }

    /**
     * Get the product variants for the product.
     */
    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Accessor to check if the product has variants.
     */
    public function getHasVariantsAttribute(): bool
    {
        return $this->variants()->exists();
    }

    /**
     * Register media collections for the product.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('product_images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg'])
            ->singleFile();

        $this->addMediaCollection('product_gallery')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg']);
    }

    /**
     * Register media conversions for the product.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('product_images', 'product_gallery');

        $this->addMediaConversion('large')
            ->width(800)
            ->height(800)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('product_images', 'product_gallery');
    }

    /**
     * Get the product's main image URL.
     *
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        // First try to get from media library
        $media = $this->getFirstMedia('product_images');
        if ($media) {
            return $media->getUrl();
        }

        // Fallback to database image_url if exists
        if (!empty($this->attributes['image_url'])) {
            return $this->attributes['image_url'];
        }

        // Final fallback to placeholder
        return asset('images/product-placeholder.svg');
    }

    /**
     * Get the product's thumbnail URL.
     *
     * @return string
     */
    public function getThumbUrlAttribute(): string
    {
        $media = $this->getFirstMedia('product_images');
        if ($media) {
            return $media->getUrl('thumb');
        }

        return asset('images/product-placeholder.svg');
    }

    /**
     * Get all product gallery images.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getGalleryImagesAttribute()
    {
        $images = collect();

        // Add main image first
        $mainImage = $this->getFirstMedia('product_images');
        if ($mainImage) {
            $images->push([
                'url' => $mainImage->getUrl(),
                'thumb' => $mainImage->getUrl('thumb'),
                'alt' => $this->name
            ]);
        }

        // Add gallery images
        $galleryImages = $this->getMedia('product_gallery');
        foreach ($galleryImages as $media) {
            $images->push([
                'url' => $media->getUrl(),
                'thumb' => $media->getUrl('thumb'),
                'alt' => $this->name
            ]);
        }

        // If no images, add placeholder
        if ($images->isEmpty()) {
            $images->push([
                'url' => asset('images/product-placeholder.svg'),
                'thumb' => asset('images/product-placeholder.svg'),
                'alt' => $this->name
            ]);
        }

        return $images;
    }

    // Consider how 'stock_quantity' on the Product model itself should behave.
    // It could be the sum of all variant stocks, or the stock of a 'default' non-variant product.
    // For now, we'll assume it's separate or managed manually if variants are not used.



    // Similarly, the price displayed might need adjustment if variants exist.
    // This could be the price of the cheapest variant, or the base price.
    // For now, the 'price' attribute on Product is the base price.
    // The ProductVariant model has a 'getPriceAttribute' for its final price.

    public function isWishlisted()
    {
        if (!auth()->check()) {
            return false;
        }

        return auth()->user()->wishlist()->where('product_id', $this->id)->exists();
    }
}
