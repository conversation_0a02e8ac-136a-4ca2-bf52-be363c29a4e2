<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Support\Facades\Storage;

class Product extends Model
{
    use HasFactory, HasSlug, SoftDeletes;

    protected $fillable = [
        'vendor_id',
        'category_id',
        'brand_id',
        'name',
        'slug',
        'description',
        'price',
        'discount_price',
        'image_url',
        'weight',
        'height',
        'width',
        'length',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    
    public function wishlists()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function wishlistedBy()
    {
        return $this->belongsToMany(User::class, 'wishlists');
    }

    public function getCurrentPrice()
    {
        return $this->discount_price > 0 ? $this->discount_price : $this->price;
    }
    
    public function getDiscountPercentage()
    {
        if (!$this->discount_price || $this->discount_price <= 0 || $this->price <= 0) {
            return 0;
        }
        
        return round((($this->price - $this->discount_price) / $this->price) * 100);
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    /**
     * Check if product is on sale
     *
     * @return bool
     */
    public function isOnSale()
    {
        return $this->discount_price > 0 && $this->discount_price < $this->price;
    }

    public function averageRating()
    {
        return $this->reviews()->avg('rating');
    }

    public function reviewCount()
    {
        return $this->reviews()->count();
    }

    /**
     * Get the product variants for the product.
     */
    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Accessor to check if the product has variants.
     */
    public function getHasVariantsAttribute(): bool
    {
        return $this->variants()->exists();
    }

    /**
     * Get the product's image URL.
     *
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        // If we have an image_url in the database, return it directly
        if (!empty($this->attributes['image_url'])) {
            return $this->attributes['image_url'];
        }

        // Try to get from file upload service if available
        try {
            $fileUploadService = app(\App\Services\FileUploadService::class);
            return $fileUploadService->getFileUrl(
                $this->attributes['image_url'] ?? null,
                'public',
                asset('images/product-placeholder.svg')
            );
        } catch (\Exception $e) {
            // Fallback to our SVG placeholder if service fails
            return asset('images/product-placeholder.svg');
        }
    }

    // Consider how 'stock_quantity' on the Product model itself should behave.
    // It could be the sum of all variant stocks, or the stock of a 'default' non-variant product.
    // For now, we'll assume it's separate or managed manually if variants are not used.



    // Similarly, the price displayed might need adjustment if variants exist.
    // This could be the price of the cheapest variant, or the base price.
    // For now, the 'price' attribute on Product is the base price.
    // The ProductVariant model has a 'getPriceAttribute' for its final price.

    public function isWishlisted()
    {
        if (!auth()->check()) {
            return false;
        }

        return auth()->user()->wishlist()->where('product_id', $this->id)->exists();
    }
}
