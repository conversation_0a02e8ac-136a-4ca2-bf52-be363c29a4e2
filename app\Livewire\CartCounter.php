<?php

namespace App\Livewire;

use Livewire\Component;

use Livewire\Attributes\On;

class CartCounter extends Component
{
    public $count = 0;

    public function mount()
    {
        $this->updateCount();
    }

    #[On('cartUpdated')]
    public function updateCount()
    {
        $cart = session()->get('cart', []);
        $this->count = count($cart);
    }

    public function render()
    {
        return view('livewire.cart-counter');
    }
}
