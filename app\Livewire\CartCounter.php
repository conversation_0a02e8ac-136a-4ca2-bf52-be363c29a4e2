<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;

class CartCounter extends Component
{
    public $count = 0;

    public function mount()
    {
        $this->updateCount();
    }

    #[On('cartUpdated')]
    #[On('cart-updated-total')]
    public function updateCount($newCount = null)
    {
        if ($newCount !== null) {
            $this->count = $newCount;
        } else {
            $cart = session()->get('cart', []);
            $this->count = count($cart);
        }
    }

    public function render()
    {
        return view('livewire.cart-counter');
    }
}
