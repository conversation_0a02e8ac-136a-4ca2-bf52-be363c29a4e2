<?php
namespace App\Models;

use App\Models\VendorTransaction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Vendor extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'shop_name',
        'slug',
        'business_name',
        'paystack_recipient_code',
        'business_address',
        'city',
        'state',
        'country',
        'logo',
        'is_approved',
        'is_featured',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function transactions()
    {
        return $this->hasMany(VendorTransaction::class);
    }
    
    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }
    
    public function subscription()
    {
        return $this->hasOne(VendorSubscription::class)->latestOfMany();
    }

    public function subscriptions()
    {
        return $this->hasMany(VendorSubscription::class);
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    public function getTotalSalesAttribute()
    {
        return $this->products()
            ->withSum('orderItems as total_sales', 'price * quantity')
            ->get()
            ->sum('total_sales');
    }

    /**
     * Get the vendor's logo URL.
     *
     * @return string
     */
    public function getLogoUrlAttribute(): string
    {
        $logoValue = $this->attributes['logo'] ?? null;

        if ($logoValue && Storage::disk('filebase')->exists($logoValue)) {
            return Storage::disk('filebase')->url($logoValue);
        }
        return asset('images/default-vendor.png'); // Default image
    }
}
