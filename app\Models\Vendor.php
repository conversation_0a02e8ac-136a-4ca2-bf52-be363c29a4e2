<?php
namespace App\Models;

use App\Models\VendorTransaction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Vendor extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;
    protected $fillable = [
        'user_id',
        'shop_name',
        'slug',
        'business_name',
        'paystack_recipient_code',
        'business_address',
        'city',
        'state',
        'country',
        'phone',
        'logo',
        'is_approved',
        'is_featured',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($vendor) {
            // Automatically create a brand for this vendor
            $vendor->brand()->create([
                'name' => $vendor->shop_name,
                'slug' => $vendor->slug,
                'description' => "Official brand for {$vendor->shop_name}",
                'is_active' => true,
                'is_featured' => $vendor->is_featured ?? false,
            ]);
        });

        static::updated(function ($vendor) {
            // Update the associated brand when vendor is updated
            if ($vendor->brand) {
                $vendor->brand->update([
                    'name' => $vendor->shop_name,
                    'slug' => $vendor->slug,
                    'is_featured' => $vendor->is_featured ?? false,
                ]);
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function transactions()
    {
        return $this->hasMany(VendorTransaction::class);
    }
    
    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }
    
    public function subscription()
    {
        return $this->hasOne(VendorSubscription::class)->latestOfMany();
    }

    public function subscriptions()
    {
        return $this->hasMany(VendorSubscription::class);
    }

    public function brand()
    {
        return $this->hasOne(Brand::class);
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    public function getTotalSalesAttribute()
    {
        return $this->products()
            ->withSum('orderItems as total_sales', 'price * quantity')
            ->get()
            ->sum('total_sales');
    }

    /**
     * Register media collections for the vendor.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg'])
            ->singleFile();
    }

    /**
     * Register media conversions for the vendor.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('logo');
    }

    /**
     * Get the vendor's logo URL.
     *
     * @return string
     */
    public function getLogoUrlAttribute(): string
    {
        // First try to get from media library
        $media = $this->getFirstMedia('logo');
        if ($media) {
            return $media->getUrl();
        }

        // Fallback to database logo if exists
        if (!empty($this->attributes['logo'])) {
            $fileUploadService = app(\App\Services\FileUploadService::class);
            return $fileUploadService->getFileUrl(
                $this->attributes['logo'],
                'public',
                asset('images/default-vendor.png')
            );
        }

        // Final fallback
        return asset('images/default-vendor.svg');
    }
}
