{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"@tailwindcss/vite": "^4.0.7", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "vite": "^6.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}}