<tr wire:key="category-{{ $category->id }}">
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
        <span style="padding-left: {{ $level * 20 }}px;">{{ $category->name }}</span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $category->slug }}</td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $category->parent->name ?? '-' }}</td>
    <td class="px-6 py-4">
        <div class="flex items-center space-x-2">
            <x-button.circle icon="pencil" flat wire:click="edit({{ $category->id }})" />
            <x-button.circle icon="trash" flat negative wire:click="$dialog.confirm({ title: 'Delete Category?', description: 'Are you sure you want to delete {{ $category->name }}?', onConfirm: () => $wire.delete({{ $category->id }}) })" />
        </div>
    </td>
</tr>

@if ($category->children->isNotEmpty())
    @foreach ($category->children as $child)
        @include('livewire.admin.categories.category-row', ['category' => $child, 'level' => $level + 1])
    @endforeach
@endif
