<?php

namespace App\Livewire\Vendor\Reviews;

use Livewire\Component;
use App\Models\Review;
use Illuminate\Support\Facades\Auth;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public function render()
    {
        $vendor = Auth::user()->vendor;
        $reviews = Review::whereHas('product', function ($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })->latest()->paginate(10);

        return view('livewire.vendor.reviews.index', [
            'reviews' => $reviews,
        ])->layout('layouts.vendor');
    }
}
