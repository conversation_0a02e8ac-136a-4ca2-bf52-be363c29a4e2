function checkout() {
    return {
        step: 1,
        totalSteps: 3,
        states: window.checkoutStates || [],
        lgas: [],
        address: {
            first_name: window.authFirstName || '',
            last_name: window.authLastName || '',
            street: window.authStreet || '',
            city: window.authCity || '',
            lga: window.authLga || '',
            state: window.authState || '',
            country: window.authCountry || 'Nigeria',
            phone: window.authPhone || '',
            email: window.authEmail || '',
        },
        paymentMethod: null,
        submitting: false,
        submissionError: null,

        loadingRates: false,
        ratesError: null,
        shippingRates: {}, // Per-vendor rates from the API
        consolidatedRates: [], // Combined rates for display
        selectedShippingRates: {}, // Final selections to be sent to the server
        selectedCourier: null, // The single courier selected by the user
        selectedCourierVendorBreakdown: [], // Detailed breakdown for the selected courier

        subtotal: window.cartSubtotal || 0,

        get totalShippingCost() {
            return this.selectedCourier ? this.selectedCourier.total : 0;
        },

        get total() {
            return this.subtotal + this.totalShippingCost;
        },

        init() {
            if (this.address.state) {
                const selectedState = this.states.find(s => s.state === this.address.state);
                if (selectedState) {
                    this.lgas = selectedState.lgas;
                }
            }

            this.$watch('address.state', (newState) => {
                this.address.lga = '';
                this.lgas = [];
                const selectedState = this.states.find(s => s.state === newState);
                if (selectedState) {
                    this.lgas = selectedState.lgas;
                }
            });

            this.$watch('address', _.debounce(() => {
                if (this.isAddressValid()) {
                    this.getRates();
                }
            }, 1000), { deep: true });
        },

        validateAndNext(nextStep) {
            if (this.step === 1 && !this.isAddressValid()) {
                // Optionally, show some validation feedback
                return;
            }
            if (this.step === 2 && !this.allVendorsHaveShippingSelected) {
                return;
            }
            this.step = nextStep;
        },

        get allVendorsHaveShippingSelected() {
            return !!this.selectedCourier;
        },

        isAddressValid() {
            return this.address.first_name && this.address.last_name && this.address.street && this.address.city && this.address.lga && this.address.state && this.address.country && this.address.phone && this.address.email;
        },

        getRates: _.debounce(function() {
            if (!this.isAddressValid()) return;

            this.loadingRates = true;
            this.ratesError = null;
            this.shippingRates = {};

            const payload = {
                name: `${this.address.first_name} ${this.address.last_name}`.trim(),
                email: this.address.email,
                phone: this.address.phone,
                address: this.address.street,
                city: this.address.city,
                lga: this.address.lga,
                state: this.address.state,
                country: this.address.country,
            };

            fetch(window.shippingRatesUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(payload)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw new Error(err.error || `Request failed with status ${response.status}`) });
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                this.shippingRates = data.rates_by_vendor;
                this.consolidatedRates = data.consolidated_rates;
                this.autoSelectCheapestRate();
            })
            .catch(error => {
                this.ratesError = error.message;
            })
            .finally(() => {
                this.loadingRates = false;
            });
        }, 1000),

        autoSelectCheapestRate() {
            if (this.consolidatedRates && this.consolidatedRates.length > 0) {
                const cheapestCourier = this.consolidatedRates.reduce((cheapest, courier) => (courier.total < cheapest.total) ? courier : cheapest, this.consolidatedRates[0]);
                this.selectCourier(cheapestCourier);
            }
        },

        selectCourier(courier) {
            this.selectedCourier = courier;
            let newSelectedRates = {};
            let breakdown = [];

            // When a consolidated courier is selected, find the corresponding rate and vendor details.
            for (const vendorId in this.shippingRates) {
                const vendorInfo = this.shippingRates[vendorId];
                if (vendorInfo.data && vendorInfo.data.couriers) {
                    const matchingRate = vendorInfo.data.couriers.find(rate => rate.courier_id === courier.courier_id);
                    if (matchingRate) {
                        newSelectedRates[vendorId] = matchingRate;
                        breakdown.push({
                            vendor_name: vendorInfo.vendor_name,
                            cost: matchingRate.total_charge
                        });
                    }
                }
            }
            this.selectedShippingRates = newSelectedRates;
            this.selectedCourierVendorBreakdown = breakdown;
        },

        isCourierSelected(courierId) {
            return this.selectedCourier && this.selectedCourier.courier_id === courierId;
        },

        submitForm(event) {
            this.submitting = true;
            this.submissionError = null;

            // Construct the payload directly from the component's state
            // to ensure all data is correctly captured, especially from x-model.
            const payload = {
                ...this.address,
                payment_method: this.paymentMethod,
                shipping_options: this.selectedShippingRates,
            };

            fetch(window.paymentInitializeUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                },
                body: JSON.stringify(payload)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { 
                        throw { status: response.status, message: err.message, errors: err.errors };
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.redirect_url) {
                    window.location.href = data.redirect_url;
                } else {
                    this.submissionError = data.message || 'An unknown error occurred.';
                    this.submitting = false;
                }
            })
            .catch(error => {
                console.error('Submission Error:', error);
                if (error.errors) {
                    const errorMessages = Object.values(error.errors).flat().join(' ');
                    this.submissionError = `Validation failed: ${errorMessages}`;
                } else {
                    this.submissionError = error.message || 'An error occurred while submitting the form.';
                }
                this.submitting = false;
            });
        },

        formatCurrency(amount) {
            return '₦' + (amount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }
    };
}
