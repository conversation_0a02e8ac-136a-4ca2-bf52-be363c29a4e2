<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class Brand extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'vendor_id',
        'name',
        'slug',
        'description',
        'logo',
        'is_active',
        'is_featured',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    // Automatically set slug when name is set
    public static function boot()
    {
        parent::boot();
        static::saving(function ($brand) {
            if (empty($brand->slug) && !empty($brand->name)) {
                $brand->slug = str_slug($brand->name);
            }
        });
    }

    // Scope for only active brands
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
    
    // Scope for featured brands
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
    
    /**
     * Get the vendor associated with this brand.
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the products associated with this brand.
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the brand's logo URL.
     *
     * @return string
     */
    public function getLogoUrlAttribute(): string
    {
        $logoValue = $this->attributes['logo'] ?? null;

        if ($logoValue && Storage::disk('public')->exists($logoValue)) {
            return Storage::url($logoValue);
        }
        return asset('images/default-brand.png'); // Default image
    }
}
