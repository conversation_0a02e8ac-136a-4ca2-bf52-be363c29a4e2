<?php

namespace App\Livewire\Admin\Vendors;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class VendorForm extends Component
{
    public ?Vendor $vendor = null;
    public ?User $user = null;

    // User fields
    public string $name = '';
    public string $email = '';
    public ?string $password = null;
    public ?string $password_confirmation = null;

    // Vendor fields
    public string $shop_name = '';
    public string $shop_email = '';
    public string $shop_phone = '';
    public string $address = '';
    public bool $is_featured = false;
    public bool $is_approved = false;

    public function mount(?Vendor $vendor = null): void
    {
        $this->vendor = $vendor;
        if ($this->vendor && $this->vendor->exists) {
            $this->user = $this->vendor->user;
            $this->name = $this->user->name;
            $this->email = $this->user->email;
            $this->shop_name = $this->vendor->shop_name;
            $this->shop_email = $this->vendor->shop_email;
            $this->shop_phone = $this->vendor->shop_phone;
            $this->address = $this->vendor->address;
            $this->is_featured = $this->vendor->is_featured;
            $this->is_approved = (bool)$this->vendor->approved_at;
        }
    }

    protected function rules(): array
    {
        $userId = $this->user?->id ?? 'NULL';
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $userId,
            'shop_name' => 'required|string|max:255',
            'shop_email' => 'required|email|max:255',
            'shop_phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'is_featured' => 'boolean',
            'is_approved' => 'boolean',
        ];

        if (!$this->vendor || !$this->vendor->exists) {
            $rules['password'] = 'required|string|min:8|confirmed';
        } else {
            $rules['password'] = 'nullable|string|min:8|confirmed';
        }

        return $rules;
    }

    public function save()
    {
        $this->validate();

        DB::transaction(function () {
            if ($this->vendor && $this->vendor->exists) {
                // Update existing user and vendor
                $this->user->update([
                    'name' => $this->name,
                    'email' => $this->email,
                ]);

                if ($this->password) {
                    $this->user->update(['password' => Hash::make($this->password)]);
                }

                $this->vendor->update([
                    'shop_name' => $this->shop_name,
                    'shop_email' => $this->shop_email,
                    'shop_phone' => $this->shop_phone,
                    'address' => $this->address,
                    'is_featured' => $this->is_featured,
                    'approved_at' => $this->is_approved ? now() : null,
                ]);

                session()->flash('success', 'Vendor updated successfully.');
            } else {
                // Create new user and vendor
                $newUser = User::create([
                    'name' => $this->name,
                    'email' => $this->email,
                    'password' => Hash::make($this->password),
                ]);

                $newUser->vendor()->create([
                    'shop_name' => $this->shop_name,
                    'shop_email' => $this->shop_email,
                    'shop_phone' => $this->shop_phone,
                    'address' => $this->address,
                    'is_featured' => $this->is_featured,
                    'approved_at' => $this->is_approved ? now() : null,
                ]);

                session()->flash('success', 'Vendor created successfully.');
            }
        });

        return redirect()->route('admin.vendors.index');
    }

    public function render()
    {
        return view('livewire.admin.vendors.vendor-form')->layout('layouts.admin');
    }
}
