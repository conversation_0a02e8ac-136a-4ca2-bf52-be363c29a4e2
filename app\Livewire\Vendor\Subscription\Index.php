<?php

namespace App\Livewire\Vendor\Subscription;

use App\Models\SubscriptionPlan;
use App\Models\VendorSubscription;
use App\Services\PaystackService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.vendor')]
class Index extends Component
{
    public $subscription;
    public $plans;
    protected $paystackService;

    public function boot(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    public function mount()
    {
        $vendor = Auth::user()->vendor;
        $this->subscription = $vendor->subscription;
        $this->plans = SubscriptionPlan::where('status', 'active')->get();
    }

    public function subscribe(SubscriptionPlan $plan)
    {
        $vendor = Auth::user()->vendor;
        $user = Auth::user();

        // Handle the free plan directly
        if ($plan->price == 0) {
            // Deactivate any existing subscriptions
            $vendor->subscriptions()->where('status', 'active')->update(['status' => 'inactive']);

            // Create a new active subscription for the free plan
            VendorSubscription::create([
                'vendor_id' => $vendor->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'active',
                'starts_at' => now(),
                'ends_at' => now()->addDays($plan->duration_days),
            ]);

            session()->flash('success', 'You have successfully subscribed to the Free Plan.');
            return $this->redirect(route('vendor.subscription.index'), navigate: true);
        }

        // For paid plans, proceed to Paystack
        $reference = 'sub_' . Str::random(10);

        $data = [
            'email' => $user->email,
            'amount' => $plan->price * 100, // Paystack amount is in kobo
            'plan' => $plan->paystack_plan_code,
            'reference' => $reference,
            'callback_url' => route('vendor.subscription.callback'),
            'metadata' => [
                'plan_id' => $plan->id,
                'vendor_id' => $vendor->id,
                'user_id' => $user->id,
            ]
        ];

        $result = $this->paystackService->initializeTransaction($data);

        if ($result && $result['status']) {
            return $this->redirect($result['data']['authorization_url']);
        }

        session()->flash('error', $result['message'] ?? 'Could not initialize subscription. Please try again.');
        return $this->redirect(route('vendor.subscription.index'), navigate: true);
    }

    public function cancel()
    {
        $vendor = Auth::user()->vendor;
        $subscription = $vendor->subscription;

        if ($subscription) {
            // This would ideally interact with Paystack's API to cancel the subscription
            // For now, we'll just mark it as cancelled locally.
            $subscription->update(['status' => 'cancelled']);
            session()->flash('success', 'Your subscription has been cancelled.');
        } else {
            session()->flash('error', 'You do not have an active subscription.');
        }
        
        return $this->redirect(route('vendor.subscription.index'), navigate: true);
    }

    public function render()
    {
        // Refresh data on render to ensure it's up to date after actions
        $this->mount(); 
        return view('livewire.vendor.subscription.index');
    }
}
