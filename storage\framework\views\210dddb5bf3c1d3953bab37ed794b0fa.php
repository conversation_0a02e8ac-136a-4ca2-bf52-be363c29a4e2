<div class="group relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
    <!-- Product Image Container -->
    <div class="relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
        <a href="<?php echo e(route('products.show', $product->slug)); ?>" class="block relative">
            <img src="<?php echo e($product->image_url); ?>"
                 alt="<?php echo e($product->name); ?>"
                 class="w-full h-72 object-cover transform group-hover:scale-110 transition-transform duration-700 ease-out"
                 onerror="this.onerror=null; this.src='<?php echo e(asset('images/product-placeholder.svg')); ?>'; this.classList.add('opacity-80');"
                 loading="lazy">

            <!-- Overlay gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </a>

        <!-- Sale Badge -->
        <!--[if BLOCK]><![endif]--><?php if($product->is_on_sale): ?>
            <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg">
                <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    SALE
                </span>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Wishlist Button -->
        <button wire:click="addToWishlist" wire:loading.attr="disabled" wire:target="addToWishlist"
                class="absolute top-4 right-4 p-2.5 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 group/wishlist">
            <span wire:loading.remove wire:target="addToWishlist">
                <!--[if BLOCK]><![endif]--><?php if($inWishlist): ?>
                    <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                    </svg>
                <?php else: ?>
                    <svg class="w-5 h-5 text-gray-600 group-hover/wishlist:text-red-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </span>
            <span wire:loading wire:target="addToWishlist">
                <svg class="w-5 h-5 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </span>
        </button>
    </div>

    <!-- Product Details -->
    <div class="p-6">
        <!-- Brand -->
        <!--[if BLOCK]><![endif]--><?php if($product->brand): ?>
            <p class="text-sm font-medium text-gray-500 mb-2 uppercase tracking-wide"><?php echo e($product->brand->name); ?></p>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Product Name -->
        <h3 class="text-lg font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
            <a href="<?php echo e(route('products.show', $product->slug)); ?>" class="hover:underline">
                <?php echo e($product->name); ?>

            </a>
        </h3>

        <!-- Price -->
        <div class="flex items-baseline justify-between mb-4">
            <!--[if BLOCK]><![endif]--><?php if($product->is_on_sale && $product->discount_price): ?>
                <div class="flex items-baseline space-x-2">
                    <span class="text-2xl font-bold text-gray-900">₦<?php echo e(number_format($product->discount_price)); ?></span>
                    <span class="text-lg text-gray-500 line-through">₦<?php echo e(number_format($product->price)); ?></span>
                </div>
                <div class="text-sm font-semibold text-green-600 bg-green-50 px-2 py-1 rounded-full">
                    Save <?php echo e(round((($product->price - $product->discount_price) / $product->price) * 100)); ?>%
                </div>
            <?php else: ?>
                <span class="text-2xl font-bold text-gray-900">₦<?php echo e(number_format($product->price)); ?></span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
        <!-- Action Buttons -->
        <div class="flex items-center space-x-3">
            <!-- Add to Cart Button -->
            <button wire:click="addToCart" wire:loading.attr="disabled" wire:target="addToCart"
                    class="flex-1 bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-xl font-semibold hover:from-black hover:to-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed group/cart">
                <span wire:loading.remove wire:target="addToCart" class="flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2 group-hover/cart:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                    Add to Cart
                </span>
                <span wire:loading wire:target="addToCart" class="flex items-center justify-center">
                    <svg class="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Adding...
                </span>
            </button>

            <!-- Quick View Button -->
            <a href="<?php echo e(route('products.show', $product->slug)); ?>"
               class="p-3 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-300 transform hover:scale-105 group/view">
                <svg class="w-5 h-5 text-gray-600 group-hover/view:text-gray-900 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
            </a>
        </div>

        <!-- Vendor Info -->
        <!--[if BLOCK]><![endif]--><?php if($product->vendor): ?>
            <div class="mt-4 pt-4 border-t border-gray-100">
                <p class="text-sm text-gray-500">
                    Sold by
                    <a href="<?php echo e(route('vendors.storefront', $product->vendor->slug)); ?>"
                       class="font-medium text-gray-900 hover:text-blue-600 transition-colors">
                        <?php echo e($product->vendor->shop_name ?? $product->vendor->brand_name); ?>

                    </a>
                </p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/product-card.blade.php ENDPATH**/ ?>