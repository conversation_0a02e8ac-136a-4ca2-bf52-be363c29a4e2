<?php $__env->startSection('content'); ?>
<div class="container mx-auto py-8">
    <h1 class="text-3xl font-bold mb-8">My Account</h1>

    <div class="lg:grid lg:grid-cols-12 lg:gap-8">
        <div class="lg:col-span-4 mb-8 lg:mb-0">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 rounded-full bg-black text-white flex items-center justify-center text-2xl font-bold mr-4">
                        <?php echo e(auth()->user()->initials()); ?>

                    </div>
                    <div>
                        <h5 class="font-bold text-lg"><?php echo e(auth()->user()->name); ?></h5>
                        <p class="text-gray-500"><?php echo e(auth()->user()->email); ?></p>
                    </div>
                </div>

                <nav class="space-y-1">
                    <a href="<?php echo e(route('dashboard')); ?>" class="<?php echo e(request()->routeIs('dashboard') ? 'bg-black text-white' : 'text-black hover:bg-gray-100'); ?> group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                    </a>
                    <a href="<?php echo e(route('orders.index')); ?>" class="<?php echo e(request()->routeIs('orders.index') || request()->routeIs('orders.show') ? 'bg-black text-white' : 'text-black hover:bg-gray-100'); ?> group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-shopping-bag mr-3"></i> My Orders
                    </a>
                    <a href="<?php echo e(route('wishlist.index')); ?>" class="<?php echo e(request()->routeIs('wishlist.index') ? 'bg-black text-white' : 'text-black hover:bg-gray-100'); ?> group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-heart mr-3"></i> My Wishlist
                    </a>
                    <a href="<?php echo e(route('settings.profile')); ?>" class="<?php echo e(request()->routeIs('settings.profile') ? 'bg-black text-white' : 'text-black hover:bg-gray-100'); ?> group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-user-cog mr-3"></i> Account Settings
                    </a>
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="w-full text-left text-red-600 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                            <i class="fas fa-sign-out-alt mr-3"></i> Logout
                        </button>
                    </form>
                </nav>
            </div>
        </div>

        <div class="lg:col-span-8">
            <?php echo $__env->yieldContent('customer-content'); ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/layouts/customer.blade.php ENDPATH**/ ?>