<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    {{ $category->name }}
                </h1>
                <p class="text-gray-300 text-lg">Discover amazing products in this category</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('products.index') }}" 
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>All Products</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Subcategories Section --}}
    @if($subcategories->count() > 0)
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-6">Subcategories</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                @foreach($subcategories as $subcategory)
                    <a href="{{ route('products.category', $subcategory->slug) }}" 
                       class="group p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all duration-300 hover:scale-105 text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-tag text-white"></i>
                        </div>
                        <p class="text-sm font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                            {{ $subcategory->name }}
                        </p>
                    </a>
                @endforeach
            </div>
        </div>
    @endif

    {{-- Search and Filter Section --}}
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {{-- Search --}}
            <div class="lg:col-span-2">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input wire:model.live.debounce.300ms="search" 
                           type="text" 
                           placeholder="Search products in {{ $category->name }}..." 
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300">
                </div>
            </div>

            {{-- Sort By --}}
            <div>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-sort text-gray-400"></i>
                    </div>
                    <select wire:model.live="sortBy" 
                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 appearance-none bg-white">
                        <option value="latest">Latest</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                        <option value="name">Name A-Z</option>
                    </select>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>

            {{-- Price Range --}}
            <div>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-dollar-sign text-gray-400"></i>
                    </div>
                    <select wire:model.live="priceRange" 
                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 appearance-none bg-white">
                        <option value="all">All Prices</option>
                        <option value="under-1000">Under ₦1,000</option>
                        <option value="1000-5000">₦1,000 - ₦5,000</option>
                        <option value="5000-10000">₦5,000 - ₦10,000</option>
                        <option value="over-10000">Over ₦10,000</option>
                    </select>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Products Grid --}}
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="p-8 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Products</h3>
                    <p class="text-gray-500 mt-1">{{ $products->total() }} products found</p>
                </div>
            </div>
        </div>

        @if($products->count() > 0)
            <div class="p-8">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    @foreach($products as $product)
                        <livewire:product-card :product="$product" :key="$product->id" />
                    @endforeach
                </div>

                {{-- Pagination --}}
                @if ($products->hasPages())
                    <div class="mt-8 pt-8 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                Showing {{ $products->firstItem() }} to {{ $products->lastItem() }} of {{ $products->total() }} results
                            </div>
                            <div class="pagination-wrapper">
                                {{ $products->links() }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @else
            <div class="p-16 text-center">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-box-open text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">No products found</h3>
                <p class="text-gray-500 mb-8 max-w-md mx-auto">
                    @if($search || $priceRange !== 'all')
                        No products match your current search criteria. Try adjusting your filters.
                    @else
                        No products are available in this category yet.
                    @endif
                </p>
                <a href="{{ route('products.index') }}" 
                   class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    <span>Browse All Products</span>
                </a>
            </div>
        @endif
    </div>
</div>
