<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Manage Brands</h1>
            <x-button wire:click="create" label="Add New Brand" black />
        </div>

        <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Logo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Products</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Featured</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse ($brands as $b)
                            <tr wire:key="{{ $b->id }}">
                                <td class="px-6 py-4"><img src="{{ $b->logo ?? asset('img/placeholder.png') }}" class="h-10 w-10 rounded-full object-cover"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $b->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $b->products_count }}</td>
                                <td class="px-6 py-4"><x-badge :label="$b->is_active ? 'Active' : 'Inactive'" :color="$b->is_active ? 'positive' : 'negative'" /></td>
                                <td class="px-6 py-4"><x-toggle :value="$b->is_featured" wire:click="toggleFeatured({{ $b->id }})" /></td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <x-button.circle icon="pencil" flat wire:click="edit({{ $b->id }})" />
                                        <x-button.circle icon="trash" flat negative wire:click="$dialog.confirm({ title: 'Delete Brand?', description: 'Are you sure you want to delete {{ $b->name }}?', onConfirm: () => $wire.delete({{ $b->id }}) })" />
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr><td colspan="6" class="text-center py-12">No brands found.</td></tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <div class="mt-6">{{ $brands->links() }}</div>
    </div>

    <x-modal.card title="{{ $brand?->exists ? 'Edit' : 'Create' }} Brand" wire:model.defer="showModal">
        <div class="space-y-4">
            <x-input label="Name" wire:model.defer="name" />
            <x-textarea label="Description" wire:model.defer="description" />
            <div class="flex items-center space-x-4">
                <div class="w-1/2">
                     <x-input type="file" label="Logo" wire:model="logo" />
                </div>
                @if ($logo)
                    <img src="{{ $logo->temporaryUrl() }}" class="w-20 h-20 object-cover rounded-full">
                @elseif($brand?->logo)
                    <img src="{{ $brand->logo }}" class="w-20 h-20 object-cover rounded-full">
                @endif
            </div>
            <x-toggle label="Active" wire:model.defer="is_active" />
            <x-toggle label="Featured" wire:model.defer="is_featured" />
        </div>
        <x-slot name="footer">
            <div class="flex justify-end gap-x-4">
                <x-button flat label="Cancel" x-on:click="close" />
                <x-button black label="Save" wire:click="save" />
            </div>
        </x-slot>
    </x-modal.card>
</div>
