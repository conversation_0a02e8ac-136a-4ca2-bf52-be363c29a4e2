<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Become a Vendor
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
            Or
            <a href="{{ route('login') }}" class="font-medium text-black hover:text-gray-800">
                sign in to your existing account
            </a>
        </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            @if (session()->has('error'))
                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            <form class="space-y-6" wire:submit.prevent="register">
                <x-input wire:model.defer="name" label="Full Name" placeholder="John Doe" />
                <x-input wire:model.defer="email" type="email" label="Email Address" placeholder="<EMAIL>" />
                <x-input wire:model.defer="password" type="password" label="Password" placeholder="********" />
                <x-input wire:model.defer="password_confirmation" type="password" label="Confirm Password" placeholder="********" />

                <div>
                    <x-button type="submit" label="Create Account" black class="w-full" />
                </div>
            </form>

            <div class="mt-6 p-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-800 rounded">
                <strong>Attention Vendors:</strong> Your <span class="font-bold">business address</span> is the most important detail! We use your address to calculate shipping rates and ensure smooth deliveries. Please provide your <span class="underline">correct and complete address</span> during onboarding.
            </div>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">
                            Already have an account?
                        </span>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="{{ route('login') }}"
                       class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Sign In
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
