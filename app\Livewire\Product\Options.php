<?php

namespace App\Livewire\Product;

use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;


class Options extends Component
{
    public Product $product;
    public $variants;
    public $selectedVariantId;
    public $selectedSize;
    public $quantity = 1;
    public $inWishlist = false;

    protected $listeners = ['variantSelected'];

    public function mount(Product $product)
    {
        $this->product = $product->load('variants.size', 'variants.color');
        $this->variants = $this->product->variants;
        
        if ($this->variants->isNotEmpty()) {
            $firstVariant = $this->variants->first();
            $this->selectedVariantId = $firstVariant->id;
            $this->selectedSize = $firstVariant->size->name ?? null;
        }

        $this->checkWishlistStatus();
    }

    public function variantSelected($variantId)
    {
        $this->selectedVariantId = $variantId;
        $this->quantity = 1; // Reset quantity when variant changes
    }

    public function selectSize($size)
    {
        $this->selectedSize = $size;
        // Find the first variant that matches the selected size and update the selected variant ID
        $variant = $this->variants->firstWhere('size.name', $this->selectedSize);
        if ($variant) {
            $this->selectedVariantId = $variant->id;
        }
    }

    public function getSelectedVariantProperty()
    {
        return ProductVariant::find($this->selectedVariantId);
    }

    public function checkWishlistStatus()
    {
        if (Auth::check()) {
            $this->inWishlist = Auth::user()->wishlist()->where('product_id', $this->product->id)->exists();
        }
    }

    public function toggleWishlist()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        if ($this->inWishlist) {
            Auth::user()->wishlist()->where('product_id', $this->product->id)->delete();
            $this->inWishlist = false;
            $this->dispatch('toast', message: 'Removed from wishlist.', type: 'info');
        } else {
            Auth::user()->wishlist()->create(['product_id' => $this->product->id]);
            $this->inWishlist = true;
            $this->dispatch('toast', message: 'Added to wishlist!', type: 'success');
        }
    }

    public function addToCart()
    {
        if (!Auth::check()) {
            $this->dispatch('toast', message: 'Please login to add items to your cart.', type: 'info');
            return $this->redirect(route('login'));
        }

        $variant = null;
        if ($this->product->variants->isNotEmpty()) {
            if (!$this->selectedVariantId) {
                $this->dispatch('toast', message: 'Please select a size.', type: 'error');
                return;
            }
            $variant = $this->product->variants()->with(['size', 'color'])->find($this->selectedVariantId);
        }

        // Use a composite key for the cart to handle variants uniquely
        $cartId = $variant ? $this->product->id . '-' . $variant->id : $this->product->id;

        $cart = session()->get('cart', []);

        // Check if item already exists in cart
        if (isset($cart[$cartId])) {
            // Just update the quantity
            $cart[$cartId]['quantity'] += $this->quantity;
        } else {
            // Add new item
            $cart[$cartId] = [
                'id' => $cartId,
                'product_id' => $this->product->id,
                'name' => $this->product->name,
                'quantity' => $this->quantity,
                'price' => $variant->price ?? $this->product->price,
                'image_url' => $this->product->image_url,
                'vendor_id' => $this->product->vendor_id,
                'attributes' => [
                    'variant_id' => $variant->id ?? null,
                    'size' => $variant->size->name ?? null,
                    'color' => $variant->color->name ?? null,
                ]
            ];
        }

        session()->put('cart', $cart);

        $this->dispatch('cartUpdated'); // For updating cart count in navbar, etc.
        $this->dispatch('toast', message: 'Product added to cart!', type: 'success');
    }

    public function incrementQuantity()
    {
        $stock = $this->selectedVariant ? $this->selectedVariant->stock : $this->product->stock;
        if ($this->quantity < $stock) {
            $this->quantity++;
        }
    }

    public function decrementQuantity()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
        }
    }

    public function render()
    {
        return view('livewire.product.options');
    }
}
