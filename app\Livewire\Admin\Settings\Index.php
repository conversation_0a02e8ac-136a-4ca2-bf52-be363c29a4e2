<?php

namespace App\Livewire\Admin\Settings;

use Livewire\Component;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;

class Index extends Component
{
    public string $site_name = '';
    public string $contact_email = '';
    public string $support_phone = '';

    public function mount()
    {
        $this->site_name = config('app.name');
        $this->contact_email = config('mail.from.address');
        $this->support_phone = config('settings.support_phone', '');
    }

    protected function rules(): array
    {
        return [
            'site_name' => 'required|string|max:255',
            'contact_email' => 'required|email|max:255',
            'support_phone' => 'nullable|string|max:255',
        ];
    }

    public function save()
    {
        $this->validate();

        $this->updateEnvFile([
            'APP_NAME' => $this->site_name,
            'MAIL_FROM_ADDRESS' => $this->contact_email,
        ]);

        $settings = ['support_phone' => $this->support_phone];
        $configPath = config_path('settings.php');
        $content = '<?php return ' . var_export($settings, true) . ';';
        File::put($configPath, $content);

        if (app()->environment('production')) {
            Artisan::call('config:cache');
        }

        session()->flash('success', 'Settings updated successfully.');
        
        return redirect()->route('admin.settings.index');
    }

    private function updateEnvFile(array $data)
    {
        $envFile = app()->environmentFilePath();
        $contents = file_get_contents($envFile);

        foreach ($data as $key => $value) {
            $value = str_contains($value, ' ') ? '"' . $value . '"' : $value;
            $key = strtoupper($key);

            if (preg_match('/^' . $key . '=(.*)/m', $contents)) {
                $contents = preg_replace('/^' . $key . '=(.*)/m', $key . '=' . $value, $contents);
            } else {
                $contents .= "\n" . $key . '=' . $value;
            }
        }

        file_put_contents($envFile, $contents);
    }

    public function render()
    {
        return view('livewire.admin.settings.index')
            ->layout('layouts.admin');
    }
}
