<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\Role;
use App\Models\User;
use App\Models\Vendor;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $vendorRole = Role::firstOrCreate(['name' => 'vendor']);
        $customerRole = Role::firstOrCreate(['name' => 'customer']);

        // Create admin user
        $admin = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Admin User',
            'password' => Hash::make('password'),
            'role_id' => $adminRole->id,
        ]);

        // Create vendor user
        $vendorUser = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Vendor User',
            'password' => Hash::make('password'),
            'role_id' => $vendorRole->id,
        ]);

        // Create vendor profile for vendor user
        Vendor::firstOrCreate([
            'user_id' => $vendorUser->id,
        ], [
            'shop_name' => 'Demo Vendor Shop',
            'slug' => 'demo-vendor-shop',
            'business_name' => 'Demo Vendor Shop',
            'business_address' => '123 Demo Street',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'country' => 'Nigeria',
            'phone' => '+2348000000000',
            'is_approved' => true,
        ]);
        
        // Run the seeders
        $this->call([
            BrandSeeder::class,
            SubscriptionPlanSeeder::class,
            VendorSeeder::class,
            ProductSeeder::class
        ]);
    }
}
