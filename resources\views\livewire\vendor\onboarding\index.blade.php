<div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-8">
            <h1 class="text-3xl font-bold text-gray-900 text-center">Vendor Onboarding</h1>
            <p class="mt-2 text-center text-gray-600">Complete the steps below to set up your store.</p>

            <!-- Progress Bar -->
            <div class="mt-8">
                <div class="relative">
                    <div class="absolute top-1/2 left-0 w-full h-1 bg-gray-200 rounded"></div>
                    <div class="absolute top-1/2 left-0 h-1 bg-black rounded"
                         style="width: {{ $step === 'business' ? '0%' : ($step === 'documents' ? '50%' : '100%') }};"></div>
                    <div class="flex justify-between items-center relative">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $step === 'business' || $step === 'documents' || $step === 'payouts' ? 'bg-black text-white' : 'bg-gray-200 text-gray-500' }}">
                                1
                            </div>
                            <p class="mt-2 text-sm font-medium">Business Info</p>
                        </div>
                        <div class="flex flex-col items-center text-center">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $step === 'documents' || $step === 'payouts' ? 'bg-black text-white' : 'bg-gray-200 text-gray-500' }}">
                                2
                            </div>
                            <p class="mt-2 text-sm font-medium">Documents</p>
                        </div>
                        <div class="flex flex-col items-center text-center">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $step === 'payouts' ? 'bg-black text-white' : 'bg-gray-200 text-gray-500' }}">
                                3
                            </div>
                            <p class="mt-2 text-sm font-medium">Payouts</p>
                        </div>
                    </div>
                </div>
            </div>

            @if (session()->has('success'))
                <div class="mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <!-- Step 1: Business Information -->
            <form wire:submit.prevent="saveBusinessInfo" class="mt-8 space-y-6 {{ $step === 'business' ? '' : 'hidden' }}">
                <h2 class="text-xl font-semibold text-gray-800">Step 1: Business Information</h2>
                <x-input wire:model.defer="business_name" label="Business Name" placeholder="Your Company LLC" />
                <x-input wire:model.defer="phone" label="Phone Number" placeholder="+****************" />
                <x-textarea wire:model.defer="business_address" label="Business Address" placeholder="123 Main St, Anytown, USA" />
                <x-textarea wire:model.defer="business_description" label="Business Description" placeholder="Tell us about your business..." />
                
                <div x-data="{ isUploading: false, progress: 0 }"
                     x-on:livewire-upload-start="isUploading = true"
                     x-on:livewire-upload-finish="isUploading = false"
                     x-on:livewire-upload-error="isUploading = false"
                     x-on:livewire-upload-progress="progress = $event.detail.progress">
                    <x-input type="file" wire:model="logo" label="Business Logo" />
                    <div x-show="isUploading" class="mt-2">
                        <progress max="100" x-bind:value="progress" class="w-full"></progress>
                    </div>
                </div>

                @if ($logo)
                    <div class="mt-4">
                        <p>Logo Preview:</p>
                        <img src="{{ $logo->temporaryUrl() }}" class="h-24 w-24 object-cover rounded-md">
                    </div>
                @elseif ($vendor->logo)
                     <div class="mt-4">
                        <p>Current Logo:</p>
                        <img src="{{ $vendor->logo }}" class="h-24 w-24 object-cover rounded-md">
                    </div>
                @endif

                <div class="flex justify-end">
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">Save and Continue</button>
                </div>
            </form>

            <!-- Step 3: Payouts -->
            <form wire:submit.prevent="savePayoutInfo" class="mt-8 space-y-6 {{ $step === 'payouts' ? '' : 'hidden' }}">
                <h2 class="text-xl font-semibold text-gray-800">Step 3: Payout Information</h2>
                <p class="text-gray-600">Provide your bank details to receive payments for your sales.</p>
                <x-input wire:model.defer="bank_name" label="Bank Name" placeholder="First National Bank" />
                <x-input wire:model.defer="bank_account_name" label="Bank Account Name" placeholder="John Doe" />

                <div class="flex justify-end">
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">Complete Onboarding</button>
                </div>
            </form>
        </div>
    </div>
</div>
