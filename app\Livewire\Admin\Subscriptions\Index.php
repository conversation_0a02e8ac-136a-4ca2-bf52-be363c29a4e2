<?php

namespace App\Livewire\Admin\Subscriptions;

use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public ?Subscription $editing = null;
    public $plans = [];

    public $plan_id;
    public $end_date;
    public $status;

    public function mount()
    {
        $this->plans = SubscriptionPlan::all();
    }

    public function edit(Subscription $subscription)
    {
        $this->editing = $subscription;
        $this->plan_id = $subscription->plan_id;
        $this->end_date = $subscription->end_date ? date('Y-m-d', strtotime($subscription->end_date)) : '';
        $this->status = $subscription->status;
    }

    public function save()
    {
        $this->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'end_date' => 'required|date',
            'status' => 'required|in:active,expired,canceled,pending',
        ]);

        if ($this->editing) {
            $this->editing->update([
                'plan_id' => $this->plan_id,
                'end_date' => $this->end_date,
                'status' => $this->status,
            ]);
        }

        $this->cancel();
        session()->flash('success', 'Subscription updated successfully.');
    }

    public function cancel()
    {
        $this->reset('editing', 'plan_id', 'end_date', 'status');
        $this->editing = null;
    }

    public function render()
    {
        $subscriptions = Subscription::with(['user', 'plan'])->latest()->paginate(15);
        return view('livewire.admin.subscriptions.index', [
            'subscriptions' => $subscriptions,
        ]);
    }
}
