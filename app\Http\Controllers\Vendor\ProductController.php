<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Color;
use App\Models\Size;
use App\Models\ProductVariant;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Display a listing of the vendor's products.
     */
    public function index()
    {
        $products = auth()->user()->vendor->products()->latest()->paginate(10);
        return view('vendor.products.index', compact('products'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create()
    {
        $vendor = auth()->user()->vendor;
        $subscription = $vendor->subscription;

        // Check if the vendor's current plan has an order limit
        if ($subscription && $subscription->subscriptionPlan && $subscription->subscriptionPlan->order_limit !== null) {
            $plan = $subscription->subscriptionPlan;
            $limit = $plan->order_limit;

            // Count orders within the current subscription period
            $orderCount = $vendor->orders()
                ->where('created_at', '>=', $subscription->starts_at)
                ->where('created_at', '<=', $subscription->ends_at)
                ->count();

            if ($orderCount >= $limit) {
                return redirect()->route('vendor.subscription.index')
                    ->with('error', 'You have reached your monthly order limit. Please upgrade your plan to add more products.');
            }
        }

        $categories = Category::all();
        $brands = Brand::all();
        $colors = Color::orderBy('name')->get();
        $sizes = Size::orderBy('name')->get();
        return view('vendor.products.create', compact('categories', 'brands', 'colors', 'sizes'));
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(Request $request)
    {
        $vendor = auth()->user()->vendor;

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'image' => 'nullable|image|max:2048',
            'is_active' => 'sometimes|boolean',
            'weight' => 'nullable|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',
            'variants' => 'nullable|array',
            'variants.*.name' => 'required_with:variants.*.value|string|max:255',
            'variants.*.value' => 'required_with:variants.*.name|string|max:255',
            'variants.*.price' => 'nullable|numeric|min:0',
            'variants.*.image' => 'nullable|image|max:2048',
            'specifications' => 'nullable|array',
            'specifications.*.name' => 'required_with:specifications.*.value|string|max:255',
            'specifications.*.value' => 'required_with:specifications.*.name|string|max:255',
        ]);

        $productData = $request->only(['name', 'category_id', 'description', 'price', 'discount_price', 'weight', 'height', 'width', 'length']);
        $productData['vendor_id'] = $vendor->id;
        if ($vendor->brand) {
            $productData['brand_id'] = $vendor->brand->id; // Auto-assign vendor's brand
        } else {
            $productData['brand_id'] = null; // Set brand_id to null if vendor has no brand
        }
        $productData['is_active'] = $request->has('is_active');
        $productData['slug'] = Str::slug($request->name) . '-' . uniqid();

        if ($request->hasFile('image')) {
            $fileUploadService = app(\App\Services\FileUploadService::class);
            $productData['image_url'] = $fileUploadService->uploadImage(
                $request->file('image'),
                'product-images',
                [
                    'resize' => ['width' => 800, 'height' => 800],
                    'quality' => 85,
                    'format' => 'webp'
                ]
            );
        }

        $product = Product::create($productData);

        if ($request->has('variants')) {
            foreach ($request->variants as $index => $variantData) {
                if (empty($variantData['name']) || empty($variantData['value'])) {
                    continue;
                }

                $newVariantData = [
                    'product_id' => $product->id,
                    'name' => $variantData['name'],
                    'value' => $variantData['value'],
                    'price' => $variantData['price'] ?? null,
                ];

                if ($request->hasFile("variants.{$index}.image")) {
                    $variantImagePath = $request->file("variants.{$index}.image")->store('variant-images', 'filebase');
                    $newVariantData['image_path'] = $variantImagePath;
                }

                $product->variants()->create($newVariantData);
            }
        }

        if ($request->has('specifications')) {
            foreach ($request->specifications as $specData) {
                if (empty($specData['name']) || empty($specData['value'])) {
                    continue;
                }
                $product->specifications()->create($specData);
            }
        }

        return redirect()->route('vendor.products.index')
            ->with('success', 'Product created successfully.');
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit(Product $product)
    {
        // Ensure the vendor can only edit their own products
        if ($product->vendor_id !== auth()->user()->vendor->id) {
            abort(403, 'Unauthorized action.');
        }

        $categories = Category::all();
        $brands = Brand::all();
        $colors = Color::orderBy('name')->get();
        $sizes = Size::orderBy('name')->get();
        $product->load('variants.color', 'variants.size');
        return view('vendor.products.edit', compact('product', 'categories', 'brands', 'colors', 'sizes'));
    }

    /**
     * Update the specified product in storage.
     */
    public function update(Request $request, Product $product)
    {
        // Ensure the vendor can only update their own products
        if ($product->vendor_id !== auth()->user()->vendor->id) {
            abort(403, 'Unauthorized action.');
        }

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'image' => 'nullable|image|max:2048',
            'is_active' => 'sometimes|boolean',
            'weight' => 'nullable|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',
            'variants' => 'nullable|array',
            'variants.*.id' => 'nullable|exists:product_variants,id',
            'variants.*.name' => 'required_with:variants.*.value|string|max:255',
            'variants.*.value' => 'required_with:variants.*.name|string|max:255',
            'variants.*.price' => 'nullable|numeric|min:0',
            'variants.*.image' => 'nullable|image|max:2048',
            'specifications' => 'nullable|array',
            'specifications.*.id' => 'nullable|exists:product_specifications,id',
            'specifications.*.name' => 'required_with:specifications.*.value|string|max:255',
            'specifications.*.value' => 'required_with:specifications.*.name|string|max:255',
        ]);

        $productData = $request->only(['name', 'category_id', 'description', 'price', 'discount_price', 'weight', 'height', 'width', 'length']);
        $productData['is_active'] = $request->has('is_active');
        $productData['slug'] = Str::slug($request->name) . '-' . uniqid();

        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($product->image_url && Storage::disk('public')->exists($product->image_url)) {
                Storage::disk('public')->delete($product->image_url);
            }
            $imagePath = $request->file('image')->store('product-images', 'public');
            $productData['image_url'] = $imagePath;
        }

        $product->update($productData);

        // Handle Variants
        $submittedVariantIds = [];
        if ($request->has('variants')) {
            foreach ($request->variants as $index => $variantData) {
                if (empty($variantData['name']) || empty($variantData['value'])) {
                    continue;
                }

                $variantDetails = [
                    'name' => $variantData['name'],
                    'value' => $variantData['value'],
                    'price' => $variantData['price'] ?? null,
                ];

                $variant = null;
                if (!empty($variantData['id'])) {
                    $variant = $product->variants()->find($variantData['id']);
                }

                if ($request->hasFile("variants.{$index}.image")) {
                    if ($variant && $variant->image_path && Storage::disk('filebase')->exists($variant->image_path)) {
                        Storage::disk('filebase')->delete($variant->image_path);
                    }
                    $variantImagePath = $request->file("variants.{$index}.image")->store('variant-images', 'filebase');
                    $variantDetails['image_path'] = $variantImagePath;
                }

                if ($variant) { // Update existing variant
                    $variant->update($variantDetails);
                    $submittedVariantIds[] = $variant->id;
                } else { // Create new variant
                    $newVariant = $product->variants()->create($variantDetails);
                    $submittedVariantIds[] = $newVariant->id;
                }
            }
        }
        // Delete variants that were not in the submission
        $variantsToDelete = $product->variants()->whereNotIn('id', $submittedVariantIds);
        foreach ($variantsToDelete->get() as $variant) {
            if ($variant->image_path && Storage::disk('filebase')->exists($variant->image_path)) {
                Storage::disk('filebase')->delete($variant->image_path);
            }
            $variant->delete();
        }


        // Handle Specifications
        $submittedSpecIds = [];
        if ($request->has('specifications')) {
            foreach ($request->specifications as $specData) {
                if (empty($specData['name']) || empty($specData['value'])) {
                    continue;
                }

                if (!empty($specData['id'])) { // Update existing
                    $spec = $product->specifications()->find($specData['id']);
                    if ($spec) {
                        $spec->update($specData);
                        $submittedSpecIds[] = $spec->id;
                    }
                } else { // Create new
                    $newSpec = $product->specifications()->create($specData);
                    $submittedSpecIds[] = $newSpec->id;
                }
            }
        }
        // Delete specifications that were not in the submission
        $product->specifications()->whereNotIn('id', $submittedSpecIds)->delete();


        return redirect()->route('vendor.products.index')
            ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified product from storage.
     */
    public function destroy(Product $product)
    {
        // Ensure the vendor can only delete their own products
        if ($product->vendor_id !== auth()->user()->vendor->id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if the product is in any orders
        if ($product->orderItems()->count() > 0) {
            return redirect()->route('vendor.products.index')
                ->with('error', 'Cannot delete product because it is associated with orders.');
        }

        // Remove product image
        if ($product->image_url && Storage::disk('public')->exists($product->image_url)) {
            Storage::disk('public')->delete($product->image_url);
        }

        $product->delete();

        return redirect()->route('vendor.products.index')
            ->with('success', 'Product deleted successfully.');
    }
}
