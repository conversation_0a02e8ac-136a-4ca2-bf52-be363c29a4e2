<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Wishlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    /**
     * Display the user's wishlist.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user = Auth::user();
        return view('customer.wishlist');
    }

    /**
     * Add a product to the user's wishlist.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Product  $product
     * @return \Illuminate\Http\Response
     */
    public function add(Product $product)
    {
        $user = Auth::user();

        if (!$user->wishlist()->where('product_id', $product->id)->exists()) {
            $wishlist = new Wishlist();
            $wishlist->user_id = $user->id;
            $wishlist->product_id = $product->id;
            $wishlist->save();

            return response()->json(['status' => 'success', 'message' => 'Product added to your wishlist.']);
        }

        return response()->json(['status' => 'info', 'message' => 'Product is already in your wishlist.']);
    }

    /**
     * Remove a product from the user's wishlist.
     *
     * @param  \App\Models\Product  $product
     * @return \Illuminate\Http\Response
     */
    public function remove(Product $product)
    {
        $user = Auth::user();

        $wishlistItem = $user->wishlist()->where('product_id', $product->id)->first();

        if ($wishlistItem) {
            $wishlistItem->delete();
            return response()->json(['status' => 'success', 'message' => 'Product removed from your wishlist.']);
        }

        return response()->json(['status' => 'error', 'message' => 'Product not found in your wishlist.'], 404);
    }

    /**
     * Clear all items from the user's wishlist.
     *
     * @return \Illuminate\Http\Response
     */
    public function clear()
    {
        $user = Auth::user();
        $user->wishlist()->delete();
        
        return redirect()->back()->with('success', 'Your wishlist has been cleared.');
    }
}
