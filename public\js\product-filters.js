document.addEventListener('DOMContentLoaded', function () {
    document.body.addEventListener('submit', function(event) {
        if (event.target.matches('.ajax-add-to-cart-form')) {
            event.preventDefault();
            const form = event.target;
            const button = form.querySelector('.add-to-cart-btn');
            const originalButtonContent = button.innerHTML;

            // Disable button and show spinner
            button.disabled = true;
            button.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>`;

            fetch(form.action, {
                method: 'POST',
                body: new FormData(form),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': form.querySelector('[name="_token"]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update cart count in the header
                    const cartCount = document.getElementById('cart-count');
                    if (cartCount) {
                        cartCount.textContent = data.cart_count;
                    }
                    // You can add a more sophisticated notification here (e.g., a toast)
                    alert(data.message || 'Product added to cart!');
                } else {
                    alert(data.message || 'An error occurred.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An unexpected error occurred. Please try again.');
            })
            .finally(() => {
                // Re-enable button and restore original content
                button.disabled = false;
                button.innerHTML = originalButtonContent;
            });
        }
    });
});
