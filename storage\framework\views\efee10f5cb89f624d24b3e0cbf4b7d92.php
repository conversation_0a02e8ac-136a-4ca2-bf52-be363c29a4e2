<?php $__env->startSection('customer-content'); ?>
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold">My Wishlist</h2>
        <p class="text-gray-500 mt-1">Your saved items and favorites.</p>
    </div>
    <div class="p-6">
        <?php
            $wishlistItems = auth()->user()->wishlist()->with('product')->get();
            $products = $wishlistItems->map(function ($item) {
                return $item->product;
            })->filter()->unique('id');
        ?>

        <?php if($products->isNotEmpty()): ?>
            <div class="mb-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product-card', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, $product->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <div class="flex justify-end mt-6 pt-6 border-t border-gray-200">
                <form action="<?php echo e(route('wishlist.clear')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 text-white border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring ring-red-300 disabled:opacity-25 transition ease-in-out duration-150">
                        Clear Wishlist
                    </button>
                </form>
            </div>
        <?php else: ?>
            <div class="text-center py-16">
                <i class="far fa-heart fa-4x text-gray-300 mb-4"></i>
                <h4 class="font-bold text-xl mt-4">Your Wishlist is Empty</h4>
                <p class="text-gray-500 mt-2">Looks like you haven't added anything to your wishlist yet. <br>Start exploring and save your favorites!</p>
                <a href="<?php echo e(route('products.index')); ?>" class="mt-6 inline-flex items-center px-4 py-2 bg-black text-white border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-gray-800 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    Start Shopping
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/customer/wishlist.blade.php ENDPATH**/ ?>