<?php

namespace App\Livewire\Product;

use App\Models\Product;
use Livewire\Component;

class Gallery extends Component
{
    public Product $product;
    public $images;
    public $selectedImage;

    public function mount(Product $product)
    {
        $this->product = $product;

        // Create an array of images - for now just use the main product image
        // In the future, this could be expanded to support multiple images
        $this->images = collect();

        if ($this->product->image_url) {
            $this->images->push($this->product->image_url);
        }

        // If no images, add placeholder
        if ($this->images->isEmpty()) {
            $this->images->push(asset('images/product-placeholder.svg'));
        }

        $this->selectedImage = $this->images->first();
    }

    public function selectImage($image)
    {
        $this->selectedImage = $image;
    }

    public function render()
    {
        return view('livewire.product.gallery');
    }
}
