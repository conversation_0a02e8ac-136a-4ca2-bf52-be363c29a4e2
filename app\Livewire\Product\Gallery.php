<?php

namespace App\Livewire\Product;

use App\Models\Product;
use Livewire\Component;

class Gallery extends Component
{
    public Product $product;
    public $images;
    public $selectedImage;

    public function mount(Product $product)
    {
        $this->product = $product;
        $this->images = $this->product->getMedia('product_images')->pluck('original_url');
        $this->selectedImage = $this->images->first();
    }

    public function selectImage($image)
    {
        $this->selectedImage = $image;
    }

    public function render()
    {
        return view('livewire.product.gallery');
    }
}
