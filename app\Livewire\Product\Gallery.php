<?php

namespace App\Livewire\Product;

use App\Models\Product;
use Livewire\Component;

class Gallery extends Component
{
    public Product $product;
    public $images;
    public $selectedImage;

    public function mount(Product $product)
    {
        $this->product = $product;

        // Get all gallery images from media library
        $this->images = $this->product->gallery_images;

        // Set the first image as selected
        $this->selectedImage = $this->images->first();
    }

    public function selectImage($imageData)
    {
        // Find the image in our collection
        $image = $this->images->firstWhere('url', $imageData);
        if ($image) {
            $this->selectedImage = $image;
        }
    }

    public function render()
    {
        return view('livewire.product.gallery');
    }
}
