@extends('layouts.app')

@section('content')
    <!-- Main Content -->
    <div class="mx-auto max-w-7xl">
        <!-- Hero Section with Split Layout -->
        <div class="relative h-[500px] md:h-[700px] bg-gray-900 overflow-hidden rounded-lg shadow-2xl">
            <!-- Content Grid -->
            <div class="relative z-10 grid h-full md:grid-cols-2">
                <!-- Left side: Text Content -->
                <div class="flex flex-col items-start justify-center h-full p-8 md:p-16 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700">
                    <div class="max-w-lg">
                        <h1 class="mb-6 text-4xl font-extrabold leading-tight text-white md:text-5xl lg:text-6xl">
                            Discover Your
                            <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Style</span>
                        </h1>
                        <p class="mb-8 text-lg text-gray-300 md:text-xl leading-relaxed">
                            Explore curated collections from Nigeria's finest brands. Quality products, authentic styles, delivered to your doorstep.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="{{ route('products.index') }}" class="px-8 py-4 font-bold text-gray-900 bg-white rounded-full transition duration-300 ease-in-out transform hover:bg-gray-100 hover:scale-105 shadow-lg">
                                Shop Now
                            </a>
                            <a href="{{ route('about') }}" class="px-8 py-4 font-bold text-white border-2 border-white rounded-full transition duration-300 ease-in-out transform hover:bg-white hover:text-gray-900 hover:scale-105">
                                Learn More
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Right side: Image Slider with Transparent Overlay -->
                <div class="relative h-full">
                    <!-- Slider Background -->
                    <div class="absolute inset-0 z-0" x-data="{ slide: 0, banners: ['/storage/banner1.jpg', '/storage/banner2.jpg', '/storage/banner3.jpg'] }" x-init="setInterval(() => { slide = (slide + 1) % banners.length }, 5000)">
                        <template x-for="(banner, idx) in banners" :key="banner">
                            <div :class="{'opacity-100': slide === idx, 'opacity-0': slide !== idx}"
                                 class="absolute inset-0 w-full h-full bg-cover bg-center transition-opacity duration-1000"
                                 :style="`background-image: url('${banner}')`">
                            </div>
                        </template>
                    </div>

                    <!-- Transparent Overlay Effect -->
                    <div class="absolute inset-0 bg-gradient-to-l from-transparent via-black/10 to-black/30 z-10"></div>

                    <!-- Slider Indicators -->
                    <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20" x-data="{ slide: 0, banners: ['/storage/banner1.jpg', '/storage/banner2.jpg', '/storage/banner3.jpg'] }" x-init="setInterval(() => { slide = (slide + 1) % banners.length }, 5000)">
                        <div class="flex space-x-2">
                            <template x-for="(banner, idx) in banners" :key="idx">
                                <button @click="slide = idx" :class="{'bg-white': slide === idx, 'bg-white/50': slide !== idx}"
                                        class="w-3 h-3 rounded-full transition-all duration-300 hover:bg-white/80">
                                </button>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Products Section -->
        @if ($featuredProducts->isNotEmpty())
            <section class="mb-16">
                <h2 class="mb-8 text-3xl font-bold text-center">Featured Products</h2>
                <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                    @foreach ($featuredProducts as $product)
                        <livewire:product-card :product="$product" :key="$product->id" />
                    @endforeach
                </div>
            </section>
        @endif

        <!-- New Arrivals Section -->
        @if ($newArrivals->isNotEmpty())
            <section class="mb-16">
                <h2 class="mb-8 text-3xl font-bold text-center">New Arrivals</h2>
                <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                    @foreach ($newArrivals as $product)
                        <livewire:product-card :product="$product" :key="$product->id" />
                    @endforeach
                </div>
            </section>
        @endif

        <!-- Featured Brands Section (Unified with Vendors) -->
        @if (($brands->isNotEmpty() || $vendors->isNotEmpty()))
            <section class="mb-16">
                <h2 class="mb-8 text-3xl font-bold text-center">Featured Brands</h2>
                <div class="grid grid-cols-1 gap-8 md:grid-cols-3">
                    @foreach ($brands as $brand)
                        <div class="p-6 text-center bg-white rounded-lg border border-gray-200 shadow-md">
                            <img src="{{ $brand->logo_url ?? asset('storage/brand-placeholder.png') }}" alt="{{ $brand->name }} Logo" class="object-contain mx-auto mb-4 w-32 h-16">
                            <h3 class="text-xl font-bold">{{ $brand->name }}</h3>
                            <a href="{{ route('products.index', ['brand' => $brand->slug]) }}" class="inline-block mt-2 text-black hover:underline">View Products</a>
                        </div>
                    @endforeach
                    @foreach ($vendors as $vendor)
                        <div class="p-6 text-center bg-white rounded-lg border border-gray-200 shadow-md">
                            <img src="{{ $vendor->logo_url ?? asset('storage/vendor-placeholder.png') }}" alt="{{ $vendor->brand_name }} Logo" class="object-contain mx-auto mb-4 w-24 h-24 rounded-full">
                            <h3 class="text-xl font-bold">{{ $vendor->brand_name }}</h3>
                            <a href="{{ route('vendors.storefront', $vendor->slug) }}" class="inline-block mt-2 text-black hover:underline">Shop Now</a>
                        </div>
                    @endforeach
                </div>
            </section>
        @endif

        <!-- Categories Section -->
        @if ($categories->isNotEmpty())
            <section class="mb-16">
                <h2 class="mb-8 text-3xl font-bold text-center">Shop by Category</h2>
                <div class="grid grid-cols-1 gap-8 md:grid-cols-3">
                    @foreach ($categories as $category)
                        <a href="{{ route('products.index', ['category' => $category->slug]) }}" class="block overflow-hidden relative rounded-lg shadow-lg group">
                            <img src="{{ $category->image_url ?? asset('storage/category-placeholder.jpg') }}" alt="{{ $category->name }}" class="object-cover w-full h-80 transition-transform duration-300 transform group-hover:scale-110">
                            <div class="flex absolute inset-0 justify-center items-center bg-black bg-opacity-50">
                                <h3 class="text-2xl font-bold text-white">{{ $category->name }}</h3>
                            </div>
                        </a>
                    @endforeach
                </div>
            </section>
        @endif

        <!-- Newsletter Section (Tailwind Only) -->
        <div class="mb-16 flex justify-center">
            <div class="w-full max-w-2xl p-8 bg-white rounded-lg shadow text-center">
                <h2 class="mb-3 text-2xl font-bold">Join Our Newsletter</h2>
                <p class="mb-4 text-gray-500">Stay updated with the latest products, exclusive offers, and fashion news.</p>
                <form class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <input type="email" class="px-4 py-3 w-full sm:w-auto flex-1 text-gray-800 bg-white rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500" placeholder="Your email address" required>
                    <button class="px-6 py-3 font-semibold text-white bg-black rounded-md transition-colors hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black" type="submit">
                        Subscribe
                    </button>
                </form>
            </div>
        </div>
    </div>
    @push('scripts')
    <!-- Hero Slider JavaScript -->
    <script src="{{ asset('js/slider-config.js') }}"></script>

    <!-- Cart AJAX JavaScript -->
    <script src="{{ asset('js/cart.js') }}"></script>
@endpush
@endsection

