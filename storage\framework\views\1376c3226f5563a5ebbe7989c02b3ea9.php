<div>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            <?php echo e(__('Shop')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Filters Sidebar -->
                <aside class="lg:col-span-1">
                    <div class="p-4 bg-white dark:bg-gray-800 shadow-sm sm:rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h3>

                        <!-- Search Filter -->
                        <div class="mb-6">
                            <input wire:model.live.debounce.300ms="search" type="text" placeholder="Search products..." class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                        </div>

                        <!-- Category Filter -->
                        <div class="mb-6">
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Category</h4>
                            <select wire:model.live="category" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="">All Categories</option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($cat->slug); ?>"><?php echo e($cat->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>

                        <!-- Brand Filter -->
                        <div class="mb-6">
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Brand</h4>
                            <select wire:model.live="brand" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="">All Brands</option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $br): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($br->slug); ?>"><?php echo e($br->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>

                        <!-- Price Range Filter -->
                        <div>
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Price Range</h4>
                            <div class="flex space-x-2">
                                <input wire:model.live.debounce.300ms="min_price" type="number" placeholder="Min" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                                <input wire:model.live.debounce.300ms="max_price" type="number" placeholder="Max" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- Products Grid -->
                <main class="lg:col-span-3">
                    <!-- Sorting -->
                    <div class="flex justify-between items-center mb-4">
                        <p class="text-sm text-gray-700 dark:text-gray-300">
                            Showing <span class="font-medium"><?php echo e($products->firstItem()); ?></span> to <span class="font-medium"><?php echo e($products->lastItem()); ?></span> of <span class="font-medium"><?php echo e($products->total()); ?></span> results
                        </p>
                        <select wire:model.live="sort_by" class="w-48 px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="latest">Sort by latest</option>
                            <option value="price_asc">Sort by price: low to high</option>
                            <option value="price_desc">Sort by price: high to low</option>
                            <option value="name_asc">Sort by name: A to Z</option>
                            <option value="name_desc">Sort by name: Z to A</option>
                        </select>
                    </div>

                    <div wire:loading.class="opacity-50" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                        <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product-card', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, $product->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="sm:col-span-2 md:col-span-3 text-center py-12">
                                <p class="text-lg text-gray-500">No products found matching your criteria.</p>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Pagination -->
                    <div class="mt-8">
                        <?php echo e($products->links()); ?>

                    </div>
                </main>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/product/index.blade.php ENDPATH**/ ?>