/* Enhanced Form Styling for Brandify */

/* Base input styling improvements */
.form-input-enhanced {
    @apply block w-full px-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-0 transition-all duration-300 bg-white;
}

.form-input-enhanced:focus {
    @apply shadow-lg transform scale-[1.02];
}

.form-input-enhanced.error {
    @apply border-red-500 focus:border-red-500;
}

/* Select styling improvements */
.form-select-enhanced {
    @apply block w-full px-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 focus:border-black focus:ring-0 transition-all duration-300 bg-white appearance-none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-select-enhanced:focus {
    @apply shadow-lg transform scale-[1.02];
}

.form-select-enhanced.error {
    @apply border-red-500 focus:border-red-500;
}

/* Textarea styling improvements */
.form-textarea-enhanced {
    @apply block w-full px-4 py-4 border-2 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-0 transition-all duration-300 bg-white resize-none;
    min-height: 120px;
}

.form-textarea-enhanced:focus {
    @apply shadow-lg transform scale-[1.02];
}

.form-textarea-enhanced.error {
    @apply border-red-500 focus:border-red-500;
}

/* Label styling improvements */
.form-label-enhanced {
    @apply block text-sm font-bold text-gray-900 mb-2;
}

/* Input with icon styling */
.form-input-with-icon {
    @apply pl-12;
}

.form-input-icon {
    @apply absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400;
}

/* Error message styling */
.form-error-message {
    @apply mt-2 text-sm text-red-600 flex items-center;
}

.form-error-icon {
    @apply mr-1;
}

/* Success message styling */
.form-success-message {
    @apply mt-2 text-sm text-green-600 flex items-center;
}

.form-success-icon {
    @apply mr-1;
}

/* Button styling improvements */
.btn-primary-enhanced {
    @apply bg-black text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-800 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2;
}

.btn-secondary-enhanced {
    @apply border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 flex items-center justify-center space-x-2;
}

.btn-danger-enhanced {
    @apply bg-red-500 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:bg-red-600 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2;
}

/* Form group styling */
.form-group-enhanced {
    @apply space-y-2;
}

/* Checkbox and radio styling */
.form-checkbox-enhanced {
    @apply w-5 h-5 text-black border-2 border-gray-300 rounded focus:ring-black focus:ring-2 transition-all duration-300;
}

.form-radio-enhanced {
    @apply w-5 h-5 text-black border-2 border-gray-300 focus:ring-black focus:ring-2 transition-all duration-300;
}

/* File input styling */
.form-file-enhanced {
    @apply block w-full text-sm text-gray-500 file:mr-4 file:py-3 file:px-6 file:rounded-xl file:border-0 file:text-sm file:font-semibold file:bg-black file:text-white hover:file:bg-gray-800 file:transition-all file:duration-300;
}

/* Search input styling */
.form-search-enhanced {
    @apply form-input-enhanced pl-12;
}

.form-search-icon {
    @apply absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none;
}

/* Input group styling */
.input-group-enhanced {
    @apply relative flex items-center;
}

.input-group-text {
    @apply px-4 py-4 bg-gray-100 border-2 border-r-0 border-gray-200 rounded-l-xl text-gray-600 font-medium;
}

.input-group-enhanced .form-input-enhanced {
    @apply rounded-l-none border-l-0;
}

/* Floating label styling */
.form-floating-enhanced {
    @apply relative;
}

.form-floating-enhanced .form-input-enhanced {
    @apply pt-6 pb-2;
}

.form-floating-label {
    @apply absolute left-4 top-4 text-gray-400 text-sm transition-all duration-300 pointer-events-none;
}

.form-floating-enhanced .form-input-enhanced:focus~.form-floating-label,
.form-floating-enhanced .form-input-enhanced:not(:placeholder-shown)~.form-floating-label {
    @apply text-xs top-2 text-gray-600;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .form-input-enhanced {
        @apply bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-white;
    }

    .form-select-enhanced {
        @apply bg-gray-800 border-gray-600 text-white focus:border-white;
    }

    .form-textarea-enhanced {
        @apply bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-white;
    }

    .form-label-enhanced {
        @apply text-white;
    }

    .btn-secondary-enhanced {
        @apply border-gray-600 text-gray-300 hover:bg-gray-800 hover:border-gray-500;
    }
}

/* Animation classes */
.form-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}

.form-pulse {
    animation: pulse 2s infinite;
}

/* Loading state */
.form-loading {
    @apply opacity-50 pointer-events-none;
}

.form-loading::after {
    content: '';
    @apply absolute inset-0 bg-white bg-opacity-50 rounded-xl;
}