<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Product Reviews
                </h1>
                <p class="text-gray-300 text-lg">See what customers are saying about your products</p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('vendor.dashboard')); ?>"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="p-8 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Reviews</h3>
                    <p class="text-gray-500 mt-1"><?php echo e($reviews->total()); ?> total reviews</p>
                </div>
            </div>
        </div>

        <!--[if BLOCK]><![endif]--><?php if($reviews->isEmpty()): ?>
            <div class="p-16 text-center">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-star text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No reviews yet</h3>
                <p class="text-gray-500 mb-6">You don't have any reviews yet. Keep providing great products and service!</p>
                <a href="<?php echo e(route('vendor.products.index')); ?>"
                   class="inline-flex items-center px-4 py-2 bg-black text-white rounded-xl font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                    <i class="fas fa-box mr-2"></i>
                    <span>Manage Products</span>
                </a>
            </div>
        <?php else: ?>
            <div class="p-8 space-y-6">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="group bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-all duration-300 border border-gray-100">
                        <div class="flex items-start space-x-6">
                            <div class="flex-shrink-0">
                                <div class="relative">
                                    <img class="h-20 w-20 object-cover rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-300"
                                         src="<?php echo e($review->product->image_url); ?>"
                                         alt="<?php echo e($review->product->name); ?>">
                                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-star text-white text-xs"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow">
                                <div class="flex items-start justify-between">
                                    <div>
                                        <h3 class="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                                            <?php echo e($review->product->name); ?>

                                        </h3>
                                        <div class="flex items-center mt-2">
                                            <div class="flex items-center">
                                                <!--[if BLOCK]><![endif]--><?php for($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star text-lg <?php echo e($i <= $review->rating ? 'text-yellow-400' : 'text-gray-300'); ?>"></i>
                                                <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                            <span class="ml-3 px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-semibold">
                                                <?php echo e($review->rating); ?>/5
                                            </span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-gray-500"><?php echo e($review->created_at->format('M d, Y')); ?></div>
                                        <div class="text-xs text-gray-400"><?php echo e($review->created_at->format('g:i A')); ?></div>
                                    </div>
                                </div>

                                <div class="mt-4 p-4 bg-white rounded-xl border border-gray-200">
                                    <div class="flex items-center mb-2">
                                        <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-user text-gray-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-900"><?php echo e($review->user->name); ?></p>
                                            <p class="text-xs text-gray-500">Verified Customer</p>
                                        </div>
                                    </div>
                                    <p class="text-gray-700 leading-relaxed"><?php echo e($review->comment); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!--[if BLOCK]><![endif]--><?php if($reviews->hasPages()): ?>
                <div class="p-8 border-t border-gray-100 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing <?php echo e($reviews->firstItem()); ?> to <?php echo e($reviews->lastItem()); ?> of <?php echo e($reviews->total()); ?> results
                        </div>
                        <div class="pagination-wrapper">
                            <?php echo e($reviews->links()); ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/vendor/reviews/index.blade.php ENDPATH**/ ?>