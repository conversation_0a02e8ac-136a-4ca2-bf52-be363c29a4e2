<?php

namespace App\Livewire\Admin\Withdrawals;

use App\Models\Withdrawal;
use App\Services\PaystackService;
use App\Services\VendorEarningsService;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    protected $earningsService;
    protected $paystackService;

    public $resolvedAccountNames = [];

    public function boot(VendorEarningsService $earningsService, PaystackService $paystackService)
    {
        $this->earningsService = $earningsService;
        $this->paystackService = $paystackService;
    }

    public function updateStatus(int $withdrawalId, string $status)
    {
        $withdrawal = Withdrawal::findOrFail($withdrawalId);

        if ($withdrawal->status !== 'pending') {
            session()->flash('error', 'This withdrawal request has already been processed.');
            return;
        }

        if (!in_array($status, ['approved', 'rejected'])) {
            session()->flash('error', 'Invalid action specified.');
            return;
        }

        if ($status === 'rejected') {
            $withdrawal->update(['status' => 'rejected', 'processed_at' => now()]);
            session()->flash('success', 'Withdrawal request has been rejected.');
            return;
        }

        if ($status === 'approved') {
            try {
                DB::beginTransaction();

                $vendor = $withdrawal->vendor;
                $recipientCode = $vendor->paystack_recipient_code;

                if (!$recipientCode) {
                    $recipientData = $this->paystackService->createTransferRecipient([
                        'name' => $withdrawal->details['account_name'],
                        'account_number' => $withdrawal->details['account_number'],
                        'bank_code' => $withdrawal->details['bank_code'],
                    ]);

                    if (!$recipientData['status']) {
                        throw new \Exception('Failed to create Paystack recipient: ' . $recipientData['message']);
                    }

                    $recipientCode = $recipientData['data']['recipient_code'];
                    $vendor->update(['paystack_recipient_code' => $recipientCode]);
                }

                $transferData = $this->paystackService->initiateTransfer([
                    'amount' => $withdrawal->amount * 100, // Amount in kobo
                    'recipient' => $recipientCode,
                    'reason' => 'Vendor withdrawal payout.',
                    'reference' => 'wdrl_' . uniqid()
                ]);

                if (!$transferData['status']) {
                    throw new \Exception('Paystack transfer failed: ' . $transferData['message']);
                }

                $this->earningsService->processWithdrawal(
                    $vendor,
                    $withdrawal->amount,
                    'Withdrawal approved and paid out via Paystack',
                    $withdrawal
                );

                $withdrawal->update([
                    'status' => 'completed',
                    'processed_at' => now(),
                    'details' => array_merge($withdrawal->details, ['paystack_transfer_code' => $transferData['data']['transfer_code']])
                ]);

                DB::commit();

                session()->flash('success', 'Withdrawal approved and payout initiated via Paystack.');

            } catch (\Exception $e) {
                DB::rollBack();
                session()->flash('error', 'An error occurred: ' . $e->getMessage());
            }
        }
    }

    public function resolveAccountName($withdrawalId)
    {
        $withdrawal = Withdrawal::findOrFail($withdrawalId);
        $accountNumber = $withdrawal->details['account_number'] ?? null;
        $bankCode = $withdrawal->details['bank_code'] ?? null;
        if ($accountNumber && $bankCode) {
            $result = $this->paystackService->resolveAccountName($accountNumber, $bankCode);
            if (isset($result['status']) && $result['status'] && isset($result['data']['account_name'])) {
                $this->resolvedAccountNames[$withdrawalId] = $result['data']['account_name'];
            } else {
                $this->resolvedAccountNames[$withdrawalId] = 'Unable to resolve';
            }
        } else {
            $this->resolvedAccountNames[$withdrawalId] = 'Missing details';
        }
    }

    public function render()
    {
        $withdrawals = Withdrawal::with('vendor')->latest()->paginate(20);

        return view('livewire.admin.withdrawals.index', [
            'withdrawals' => $withdrawals,
        ]);
    }
}
