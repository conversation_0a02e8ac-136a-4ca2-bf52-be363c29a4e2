<?php

namespace App\Livewire\Vendor\Products;

use App\Models\Brand;
use App\Models\Category;
use App\Models\Color;
use App\Models\Product;
use App\Models\Size;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;

class ProductForm extends Component
{
    use WithFileUploads;

    public Product $product;
    public $image;
    public $variants = [];
    public $specifications = [];

    public $categories;
    public $brands;
    public $colors;
    public $sizes;

    public function mount(Product $product = null)
    {
        $this->product = $product ?? new Product();
        if ($this->product->exists) {
            foreach ($this->product->variants as $variant) {
                $this->variants[] = ['id' => $variant->id, 'name' => $variant->name, 'value' => $variant->value, 'price' => $variant->price, 'image' => null, 'existing_image_path' => $variant->image_path];
            }
            foreach ($this->product->specifications as $specification) {
                $this->specifications[] = ['id' => $specification->id, 'name' => $specification->name, 'value' => $specification->value];
            }
        } else {
            $this->product->is_active = true; // Default to active
        }

        $this->categories = Category::all();
        $this->brands = Brand::all();
        $this->colors = Color::orderBy('name')->get();
        $this->sizes = Size::orderBy('name')->get();
    }

    protected function rules()
    {
        return [
            'product.name' => 'required|string|max:255',
            'product.category_id' => 'required|exists:categories,id',
            'product.description' => 'required|string',
            'product.price' => 'required|numeric|min:0',
            'product.discount_price' => 'nullable|numeric|min:0|lt:product.price',
            'image' => 'nullable|image|max:2048',
            'product.is_active' => 'sometimes|boolean',
            'product.weight' => 'nullable|numeric|min:0',
            'product.length' => 'nullable|numeric|min:0',
            'product.width' => 'nullable|numeric|min:0',
            'product.height' => 'nullable|numeric|min:0',
            'variants' => 'nullable|array',
            'variants.*.name' => 'required_with:variants.*.value|string|max:255',
            'variants.*.value' => 'required_with:variants.*.name|string|max:255',
            'variants.*.price' => 'nullable|numeric|min:0',
            'variants.*.image' => 'nullable|image|max:2048',
            'specifications' => 'nullable|array',
            'specifications.*.name' => 'required_with:specifications.*.value|string|max:255',
            'specifications.*.value' => 'required_with:specifications.*.name|string|max:255',
        ];
    }

    public function addVariant()
    {
        $this->variants[] = ['id' => null, 'name' => '', 'value' => '', 'price' => '', 'image' => null, 'existing_image_path' => null];
    }

    public function removeVariant($index)
    {
        unset($this->variants[$index]);
        $this->variants = array_values($this->variants);
    }

    public function addSpecification()
    {
        $this->specifications[] = ['id' => null, 'name' => '', 'value' => ''];
    }

    public function removeSpecification($index)
    {
        unset($this->specifications[$index]);
        $this->specifications = array_values($this->specifications);
    }

    public function save()
    {
        $this->validate();

        $vendor = auth()->user()->vendor;
        $this->product->vendor_id = $vendor->id;

        if ($vendor->brand) {
            $this->product->brand_id = $vendor->brand->id;
        }

        if (!$this->product->exists) {
            $this->product->slug = Str::slug($this->product->name) . '-' . uniqid();
        }

        if ($this->image) {
            if ($this->product->image_url && Storage::disk('filebase')->exists($this->product->image_url)) {
                Storage::disk('filebase')->delete($this->product->image_url);
            }
            $this->product->image_url = $this->image->store('product-images', 'filebase');
        }

        $this->product->save();

        // Handle Variants
        $submittedVariantIds = [];
        foreach ($this->variants as $index => $variantData) {
            if (empty($variantData['name']) || empty($variantData['value'])) {
                continue;
            }

            $variantDetails = [
                'name' => $variantData['name'],
                'value' => $variantData['value'],
                'price' => $variantData['price'] ?? null,
            ];

            if (!empty($variantData['image'])) {
                if (!empty($variantData['id'])) {
                    $variant = $this->product->variants()->find($variantData['id']);
                    if ($variant && $variant->image_path && Storage::disk('filebase')->exists($variant->image_path)) {
                        Storage::disk('filebase')->delete($variant->image_path);
                    }
                }
                $variantDetails['image_path'] = $variantData['image']->store('variant-images', 'filebase');
            }

            $variant = $this->product->variants()->updateOrCreate(['id' => $variantData['id']], $variantDetails);
            $submittedVariantIds[] = $variant->id;
        }
        $this->product->variants()->whereNotIn('id', $submittedVariantIds)->each(function ($variant) {
            if ($variant->image_path && Storage::disk('filebase')->exists($variant->image_path)) {
                Storage::disk('filebase')->delete($variant->image_path);
            }
            $variant->delete();
        });

        // Handle Specifications
        $submittedSpecIds = [];
        foreach ($this->specifications as $specData) {
            if (empty($specData['name']) || empty($specData['value'])) {
                continue;
            }
            $spec = $this->product->specifications()->updateOrCreate(['id' => $specData['id']], $specData);
            $submittedSpecIds[] = $spec->id;
        }
        $this->product->specifications()->whereNotIn('id', $submittedSpecIds)->delete();

        session()->flash('success', 'Product saved successfully.');

        return redirect()->route('vendor.products.index');
    }

    public function render()
    {
        return view('livewire.vendor.products.product-form')
            ->layout('layouts.vendor');
    }
}
