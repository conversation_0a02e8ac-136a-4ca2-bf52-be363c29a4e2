<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'vendor_id',
        'order_number',
        'total',
        'status',
        'payment_status',
        'payment_method',
        'shipping_address',
        'shipping_name',
        'shipping_city',
        'shipping_state',
        'shipping_postal_code',
        'shipping_country',
        'shipping_phone',
        'shipping_method',
        'shipping_lga',
        'shipping_cost',
        'shipbubble_token',
        'shipping_courier_id',
        'shipping_service_code',
        'shipping_tracking_url',
        'shipping_provider_order_id',
        'billing_address',
    ];
    
    protected $casts = [
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
    ];
    
    /**
     * Boot function to handle order events
     */
    protected static function boot()
    {
        parent::boot();
        
        // Auto-generate order number when creating a new order
        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = 'ORD-' . uniqid();
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
    
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function getStatusClassAttribute()
    {
        switch ($this->status) {
            case 'completed':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'processing':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'shipped':
                return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
            case 'cancelled':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
            case 'pending':
            default:
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        }
    }
    
    public function payment()
    {
        return $this->hasOne(Payment::class);
    }
    
    public function isPending()
    {
        return $this->status === 'pending';
    }
    
    public function isProcessing()
    {
        return $this->status === 'processing';
    }
    
    public function isCompleted()
    {
        return $this->status === 'completed';
    }
    
    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }
    
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }

    public function getAddressAttribute()
    {
        return $this->shipping_name;
    }

    public function getCityAttribute()
    {
        return $this->shipping_city;
    }

    public function getLgaAttribute()
    {
        return $this->shipping_lga;
    }

    public function getStateAttribute()
    {
        return $this->shipping_state;
    }

    public function getPostalCodeAttribute()
    {
        return $this->shipping_postal_code;
    }

    public function getStatusColorAttribute()
    {
        return [
            'pending' => 'warning',
            'processing' => 'info',
            'shipped' => 'primary',
            'completed' => 'success',
            'cancelled' => 'danger',
            'refunded' => 'secondary',
        ][$this->status] ?? 'secondary';
    }
}
