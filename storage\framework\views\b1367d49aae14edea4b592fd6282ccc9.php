<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    My Wishlist
                </h1>
                <p class="text-gray-300 text-lg">Your saved items and favorites</p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('products.index')); ?>" 
                   class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center space-x-2">
                    <i class="fa-solid fa-shopping-cart transition-transform duration-300 group-hover:bounce"></i>
                    <span>Continue Shopping</span>
                </a>
            </div>
        </div>
    </div>

    
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="p-8 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Saved Items</h3>
                    <p class="text-gray-500 mt-1"><?php echo e($wishlistItems->total()); ?> items in your wishlist</p>
                </div>
            </div>
        </div>

        <!--[if BLOCK]><![endif]--><?php if($wishlistItems->count() > 0): ?>
            <div class="p-8">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $wishlistItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wishlistItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <!--[if BLOCK]><![endif]--><?php if($wishlistItem->product): ?>
                            <div class="group bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                                <div class="relative">
                                    <img src="<?php echo e($wishlistItem->product->image_url); ?>" 
                                         alt="<?php echo e($wishlistItem->product->name); ?>" 
                                         class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500">
                                    
                                    
                                    <button wire:click="removeFromWishlist(<?php echo e($wishlistItem->product->id); ?>)" 
                                            class="absolute top-4 right-4 p-2.5 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 hover:scale-110 transition-all duration-300">
                                        <i class="fas fa-times text-sm"></i>
                                    </button>

                                    
                                    <!--[if BLOCK]><![endif]--><?php if($wishlistItem->product->isOnSale()): ?>
                                        <div class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                            -<?php echo e($wishlistItem->product->getDiscountPercentage()); ?>%
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>

                                <div class="p-6">
                                    <div class="mb-3">
                                        <p class="text-sm text-gray-500 font-medium"><?php echo e($wishlistItem->product->category->name ?? 'Uncategorized'); ?></p>
                                        <h3 class="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                                            <?php echo e($wishlistItem->product->name); ?>

                                        </h3>
                                    </div>

                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center space-x-2">
                                            <!--[if BLOCK]><![endif]--><?php if($wishlistItem->product->isOnSale()): ?>
                                                <span class="text-xl font-bold text-red-600"><?php echo '₦' . number_format($wishlistItem->product->discount_price, 2); ?></span>
                                                <span class="text-sm text-gray-500 line-through"><?php echo '₦' . number_format($wishlistItem->product->price, 2); ?></span>
                                            <?php else: ?>
                                                <span class="text-xl font-bold text-gray-900"><?php echo '₦' . number_format($wishlistItem->product->price, 2); ?></span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>

                                    <div class="flex space-x-2">
                                        <a href="<?php echo e(route('products.show', $wishlistItem->product->slug)); ?>" 
                                           class="flex-1 bg-black text-white text-center py-3 px-4 rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                                            View Product
                                        </a>
                                    </div>

                                    <div class="mt-3 text-center">
                                        <p class="text-xs text-gray-500">
                                            Added <?php echo e($wishlistItem->created_at->diffForHumans()); ?>

                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                
                <!--[if BLOCK]><![endif]--><?php if($wishlistItems->hasPages()): ?>
                    <div class="mt-8 pt-8 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                Showing <?php echo e($wishlistItems->firstItem()); ?> to <?php echo e($wishlistItems->lastItem()); ?> of <?php echo e($wishlistItems->total()); ?> results
                            </div>
                            <div class="pagination-wrapper">
                                <?php echo e($wishlistItems->links()); ?>

                            </div>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php else: ?>
            <div class="p-16 text-center">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-heart text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Your wishlist is empty</h3>
                <p class="text-gray-500 mb-8 max-w-md mx-auto">
                    Start adding products to your wishlist by clicking the heart icon on any product you love.
                </p>
                <a href="<?php echo e(route('products.index')); ?>" 
                   class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    <span>Start Shopping</span>
                </a>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/wishlist/index.blade.php ENDPATH**/ ?>