<?php

namespace App\Livewire\Admin\Categories;

use App\Models\Category;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithPagination;
use WireUi\Traits\WireUiActions;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination, WireUiActions;

    public ?Category $category = null;
    public bool $showModal = false;

    public string $name = '';
    public string $slug = '';
    public ?int $parent_id = null;

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:categories,slug,' . ($this->category?->id ?? ''),
            'parent_id' => 'nullable|exists:categories,id',
        ];
    }

    public function updatedName($value)
    {
        $this->slug = Str::slug($value);
    }

    public function create()
    {
        $this->resetValidation();
        $this->reset('name', 'slug', 'parent_id', 'category');
        $this->category = new Category();
        $this->showModal = true;
    }

    public function edit(Category $category)
    {
        $this->resetValidation();
        $this->category = $category;
        $this->name = $category->name;
        $this->slug = $category->slug;
        $this->parent_id = $category->parent_id;
        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'slug' => $this->slug,
            'parent_id' => $this->parent_id,
        ];

        if ($this->category->exists) {
            $this->category->update($data);
            $this->notification()->success('Category updated successfully.');
        } else {
            Category::create($data);
            $this->notification()->success('Category created successfully.');
        }

        $this->showModal = false;
    }

    public function delete(Category $category)
    {
        if ($category->children()->count() > 0 || $category->products()->count() > 0) {
            $this->notification()->error('Cannot delete category with children or products.');
            return;
        }

        $category->delete();
        $this->notification()->success('Category deleted successfully.');
    }

    public function render()
    {
        return view('livewire.admin.categories.index', [
            'categories' => Category::with('children.children')->whereNull('parent_id')->orderBy('name')->get(),
            'allCategories' => Category::orderBy('name')->get(),
        ]);
    }
}
