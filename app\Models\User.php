<?php
namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'role_id',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function isAdmin()
    {
        return $this->role_id === 1 || (is_object($this->role) && $this->role->name === 'admin');
    }
    
    public function isVendor()
    {
        return $this->role_id === 2 || (is_object($this->role) && $this->role->name === 'vendor');
    }
    
    public function vendor()
    {
        return $this->hasOne(Vendor::class);
    }
    
    public function isApprovedVendor()
    {
        return $this->isVendor() && $this->vendor && $this->vendor->is_approved;
    }
    
    public function wishlist()
    {
        return $this->hasMany(Wishlist::class);
    }
    
    public function wishlistItems()
    {
        return $this->hasManyThrough(WishlistItem::class, Wishlist::class);
    }
    
    public function orders()
    {
        return $this->hasMany(Order::class);
    }
    
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the user's most recent shipping address from their latest order.
     */
    public function shippingAddress()
    {
        return $this->hasOne(Order::class)->latestOfMany();
    }
    
    /**
     * Get user's initials.
     *
     * @return string
     */
    public function initials()
    {
        $name = $this->name;
        $words = explode(' ', $name);
        $initials = '';
        
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }
        
        return $initials ?: strtoupper(substr($name, 0, 1));
    }
}

