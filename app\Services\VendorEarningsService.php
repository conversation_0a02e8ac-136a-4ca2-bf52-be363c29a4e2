<?php

namespace App\Services;

use App\Models\Vendor;
use App\Models\Order;
use App\Models\VendorTransaction;
use Illuminate\Support\Facades\DB;

class VendorEarningsService
{
    public function creditSale(Vendor $vendor, Order $order, float $amount, string $description)
    {
        return $this->createTransaction($vendor, 'sale', $amount, $description, $order);
    }

    public function debitCommission(Vendor $vendor, Order $order, float $amount, string $description)
    {
        return $this->createTransaction($vendor, 'commission', -$amount, $description, $order);
    }

    public function processWithdrawal(Vendor $vendor, float $amount, string $description, $withdrawal)
    {
        if ($vendor->balance < $amount) {
            throw new \Exception('Insufficient funds for withdrawal.');
        }

        return $this->createTransaction($vendor, 'withdrawal', -$amount, $description, $withdrawal);
    }

    protected function createTransaction(Vendor $vendor, string $type, float $amount, string $description, $reference)
    {
        return DB::transaction(function () use ($vendor, $type, $amount, $description, $reference) {
            $vendor->lockForUpdate();

            $newBalance = $vendor->balance + $amount;

            $transaction = $vendor->transactions()->create([
                'type' => $type,
                'amount' => $amount,
                'balance_after' => $newBalance,
                'description' => $description,
                'reference_type' => get_class($reference),
                'reference_id' => $reference->id,
            ]);

            $vendor->update(['balance' => $newBalance]);

            return $transaction;
        });
    }

    /**
     * Credit a vendor's account for a specific reason (e.g., refund).
     *
     * @param Vendor $vendor
     * @param float $amount
     * @param string $description
     * @param mixed|null $reference
     * @return VendorTransaction
     */
    public function creditVendor(Vendor $vendor, float $amount, string $description, $reference = null): VendorTransaction
    {
        return DB::transaction(function () use ($vendor, $amount, $description, $reference) {
            $vendor->balance += $amount;
            $vendor->save();

            return VendorTransaction::create([
                'vendor_id' => $vendor->id,
                'type' => 'credit',
                'amount' => $amount,
                'description' => $description,
                'balance_after_transaction' => $vendor->balance,
                'reference_id' => $reference ? $reference->id : null,
                'reference_type' => $reference ? get_class($reference) : null,
                'status' => 'completed',
            ]);
        });
    }
}
