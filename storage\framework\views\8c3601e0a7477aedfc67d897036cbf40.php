<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Profile Settings
                </h1>
                <p class="text-gray-300 text-lg">Update your personal information and preferences</p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('dashboard')); ?>"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8">
        <div class="p-8 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Personal Information</h3>
                    <p class="text-gray-500 mt-1">Update your name and email address</p>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                    <?php echo e(strtoupper(substr(Auth::user()->name, 0, 2))); ?>

                </div>
            </div>
        </div>

        <div class="p-8">
            <form wire:submit="updateProfileInformation" class="space-y-6">
                
                <div x-data="{ show: false }" x-show="show" x-transition x-init="window.Livewire.find('<?php echo e($_instance->getId()); ?>').on('profile-updated', () => { show = true; setTimeout(() => show = false, 3000) })">
                    <div class="rounded-2xl bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 p-6 shadow-lg">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-500 rounded-full mr-4">
                                <i class="fas fa-check text-white"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-green-800">Profile Updated!</h4>
                                <p class="text-sm text-green-700 mt-1">Your profile information has been saved successfully.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label for="name" class="block text-sm font-bold text-gray-900 mb-2">
                            <i class="fas fa-user mr-2 text-gray-500"></i>Full Name
                        </label>
                        <div class="relative">
                            <input type="text"
                                   wire:model.live.debounce.300ms="name"
                                   id="name"
                                   autocomplete="name"
                                   placeholder="Enter your full name"
                                   class="block w-full rounded-xl border-2 border-gray-200 px-4 py-3 text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-0 transition-all duration-300 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   required>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                </p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    
                    <div>
                        <label for="email" class="block text-sm font-bold text-gray-900 mb-2">
                            <i class="fas fa-envelope mr-2 text-gray-500"></i>Email Address
                        </label>
                        <div class="relative">
                            <input type="email"
                                   wire:model.live.debounce.300ms="email"
                                   id="email"
                                   autocomplete="email"
                                   placeholder="Enter your email address"
                                   class="block w-full rounded-xl border-2 border-gray-200 px-4 py-3 text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-0 transition-all duration-300 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   required>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i><?php echo e($message); ?>

                                </p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>

                
                <!--[if BLOCK]><![endif]--><?php if(auth()->user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail &&! auth()->user()->hasVerifiedEmail()): ?>
                    <div class="rounded-2xl bg-gradient-to-r from-yellow-50 to-orange-50 border-l-4 border-yellow-500 p-6 shadow-lg">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-500 rounded-full mr-4">
                                <i class="fas fa-exclamation-triangle text-white"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-lg font-semibold text-yellow-800">Email Verification Required</h4>
                                <p class="text-sm text-yellow-700 mt-1">Your email address is unverified. Please check your inbox for a verification email.</p>
                                <button type="button"
                                        wire:click.prevent="resendVerificationNotification"
                                        class="mt-3 inline-flex items-center px-4 py-2 bg-yellow-500 text-white rounded-lg font-medium hover:bg-yellow-600 transition-all duration-300 hover:scale-105">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    Resend Verification Email
                                </button>
                            </div>
                        </div>
                    </div>

                    <!--[if BLOCK]><![endif]--><?php if(session('status') === 'verification-link-sent'): ?>
                        <div class="rounded-2xl bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 p-6 shadow-lg">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-500 rounded-full mr-4">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-green-800">Verification Email Sent!</h4>
                                    <p class="text-sm text-green-700 mt-1">A new verification link has been sent to your email address.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                
                <div class="flex items-center justify-between pt-6 border-t border-gray-100">
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        Changes will be saved to your account immediately
                    </div>
                    <div class="flex items-center space-x-4">
                        <div wire:loading wire:target="updateProfileInformation" class="flex items-center text-blue-600">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                            <span class="text-sm font-medium">Saving...</span>
                        </div>
                        <button type="submit"
                                wire:loading.attr="disabled"
                                wire:target="updateProfileInformation"
                                class="group bg-black text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-800 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2">
                            <i class="fas fa-save transition-transform duration-300 group-hover:scale-110"></i>
                            <span>Save Changes</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    
    <div class="bg-white rounded-2xl shadow-lg border border-red-200 overflow-hidden">
        <div class="p-8 border-b border-red-100 bg-red-50">
            <div class="flex items-center">
                <div class="p-3 bg-red-500 rounded-full mr-4">
                    <i class="fas fa-exclamation-triangle text-white"></i>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-red-900">Danger Zone</h3>
                    <p class="text-red-700 mt-1">Permanently delete your account and all associated data</p>
                </div>
            </div>
        </div>
        <div class="p-8">
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('settings.delete-user-form', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/settings/profile.blade.php ENDPATH**/ ?>