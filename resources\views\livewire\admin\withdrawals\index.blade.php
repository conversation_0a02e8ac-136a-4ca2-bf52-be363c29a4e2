<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Withdrawal Requests</h1>

        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700/50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Vendor</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Bank Details</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Requested</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Processed</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($withdrawals as $withdrawal)
                            <tr wire:key="{{ $withdrawal->id }}">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $withdrawal->vendor->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">₦{{ number_format($withdrawal->amount, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    @if(!empty($withdrawal->details))
                                        <strong>Bank:</strong> {{ $withdrawal->details['bank_name'] ?? 'N/A' }}<br>
                                        <strong>Account:</strong> {{ $withdrawal->details['account_number'] ?? 'N/A' }}<br>
                                        <strong>Name:</strong> {{ $withdrawal->details['account_name'] ?? 'N/A' }}<br>
                                        <div class="mt-2">
                                            <x-button small wire:click="resolveAccountName({{ $withdrawal->id }})" label="Resolve Account Name" />
                                            @if(isset($resolvedAccountNames[$withdrawal->id]))
                                                <div class="text-xs mt-1 text-blue-600">Resolved: {{ $resolvedAccountNames[$withdrawal->id] }}</div>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-gray-500">No details</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <x-badge :label="ucfirst($withdrawal->status)" 
                                        :color="$withdrawal->status === 'completed' ? 'positive' : ($withdrawal->status === 'pending' ? 'warning' : 'negative')" />
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $withdrawal->created_at->format('d M, Y') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $withdrawal->processed_at ? $withdrawal->processed_at->format('d M, Y') : 'N/A' }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    @if ($withdrawal->status === 'pending')
                                        <div class="flex items-center space-x-2">
                                            @if(isset($resolvedAccountNames[$withdrawal->id]) && $resolvedAccountNames[$withdrawal->id] !== 'Unable to resolve' && $resolvedAccountNames[$withdrawal->id] !== 'Missing details')
                                                <x-button positive wire:click="updateStatus({{ $withdrawal->id }}, 'approved')" label="Approve" />
                                            @else
                                                <span class="text-xs text-gray-500">Resolve account name to approve</span>
                                            @endif
                                            <x-button negative wire:click="updateStatus({{ $withdrawal->id }}, 'rejected')" label="Reject" />
                                        </div>
                                    @else
                                        <span class="text-gray-500">Processed</span>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center text-sm text-gray-500 dark:text-gray-400">No withdrawal requests found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <div class="mt-6">
            {{ $withdrawals->links() }}
        </div>
    </div>
</div>
