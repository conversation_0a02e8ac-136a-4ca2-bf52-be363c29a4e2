<div>
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Shop Settings</h1>
    </div>

    @if (session()->has('success'))
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
            <p>{{ session('success') }}</p>
        </div>
    @endif

    <form wire:submit.prevent="updateSettings" class="bg-white p-8 rounded-lg shadow-md">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Business Information -->
            <div class="md:col-span-2">
                <h2 class="text-lg font-semibold text-gray-700 border-b pb-2 mb-4">Business Information</h2>
            </div>

            <div>
                <label for="shop_name" class="block text-sm font-medium text-gray-700">Shop Name</label>
                <input type="text" id="shop_name" wire:model.lazy="shop_name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                @error('shop_name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="business_name" class="block text-sm font-medium text-gray-700">Legal Business Name</label>
                <input type="text" id="business_name" wire:model.lazy="business_name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                @error('business_name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div class="md:col-span-2">
                <label for="business_address" class="block text-sm font-medium text-gray-700">Business Address</label>
                <input type="text" id="business_address" wire:model.lazy="business_address" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                @error('business_address') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                <input type="text" id="city" wire:model.lazy="city" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                @error('city') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="state" class="block text-sm font-medium text-gray-700">State / Province</label>
                <input type="text" id="state" wire:model.lazy="state" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                @error('state') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

             <div>
                <label for="country" class="block text-sm font-medium text-gray-700">Country</label>
                <input type="text" id="country" wire:model.lazy="country" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm">
                @error('country') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <!-- Logo Upload -->
            <div class="md:col-span-2">
                <h2 class="text-lg font-semibold text-gray-700 border-b pb-2 mb-4 mt-6">Shop Logo</h2>
                <div class="flex items-center space-x-6">
                    <div class="shrink-0">
                        @if ($logo)
                            <img class="h-20 w-20 object-cover rounded-full" src="{{ $logo->temporaryUrl() }}" alt="New Logo Preview">
                        @elseif ($vendor->logo)
                            <img class="h-20 w-20 object-cover rounded-full" src="{{ Storage::disk('filebase')->url($vendor->logo) }}" alt="Current Logo">
                        @else
                            <div class="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center">
                                <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14"></path></svg>
                            </div>
                        @endif
                    </div>
                    <label class="block">
                        <span class="sr-only">Choose logo</span>
                        <input type="file" wire:model="logo" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-black file:text-white hover:file:bg-gray-800"/>
                    </label>
                </div>
                @error('logo') <span class="text-red-500 text-xs mt-2">{{ $message }}</span> @enderror
                 <div wire:loading wire:target="logo" class="text-sm text-gray-500 mt-2">Uploading...</div>
            </div>
        </div>

        <div class="mt-8 text-right">
            <button type="submit" wire:loading.attr="disabled" class="bg-black text-white font-bold py-2 px-6 rounded-lg hover:bg-gray-800 transition duration-300">
                <span wire:loading.remove>Save Changes</span>
                <span wire:loading>Saving...</span>
            </button>
        </div>
    </form>
</div>
