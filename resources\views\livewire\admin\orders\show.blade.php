<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Order #{{ $order->order_number }}</h1>
            <a href="{{ route('admin.orders.index') }}" class="text-sm text-gray-600 dark:text-gray-400 hover:underline">&larr; Back to Orders</a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold">Order Items</h3>
                    </div>
                    <div class="divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($order->items as $item)
                            <div class="p-4 flex items-center space-x-4">
                                <img src="{{ $item->product->featured_image->getUrl('thumb') }}" alt="{{ $item->product->name }}" class="w-16 h-16 rounded object-cover">
                                <div class="flex-grow">
                                    <p class="font-semibold text-gray-900 dark:text-white">{{ $item->product->name }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">SKU: {{ $item->product->sku }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold">{{ $item->formatted_price }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Qty: {{ $item->quantity }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="p-6 bg-gray-50 dark:bg-gray-700/50 rounded-b-lg text-right">
                        <p class="text-gray-600 dark:text-gray-300">Subtotal: <span class="font-semibold">{{ $order->formatted_subtotal }}</span></p>
                        <p class="text-gray-600 dark:text-gray-300">Shipping: <span class="font-semibold">{{ $order->formatted_shipping_cost }}</span></p>
                        @if($order->discount_code)
                        <p class="text-gray-600 dark:text-gray-300">Discount ({{ $order->discount_code }}): <span class="font-semibold">-{{ $order->formatted_discount }}</span></p>
                        @endif
                        <p class="text-xl font-bold text-gray-900 dark:text-white mt-2">Total: <span class="font-semibold">{{ $order->formatted_total }}</span></p>
                    </div>
                </div>
            </div>

            <div>
                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold">Customer Details</h3>
                    </div>
                    <div class="p-6 space-y-2">
                        <p class="font-semibold">{{ $order->user->name }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $order->user->email }}</p>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold">Shipping Address</h3>
                    </div>
                    <div class="p-6 text-sm text-gray-600 dark:text-gray-400">
                        @if($order->shippingAddress)
                            <p>{{ $order->shippingAddress->address_line_1 }}</p>
                            @if($order->shippingAddress->address_line_2)<p>{{ $order->shippingAddress->address_line_2 }}</p>@endif
                            <p>{{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }} {{ $order->shippingAddress->postal_code }}</p>
                            <p>{{ $order->shippingAddress->country }}</p>
                        @else
                            <p>No shipping address provided.</p>
                        @endif
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold">Update Status</h3>
                    </div>
                    <div class="p-6">
                        <x-native-select
                            label="Order Status"
                            :options="['pending', 'processing', 'shipped', 'delivered', 'cancelled']"
                            wire:model.defer="status"
                        />
                        <x-button wire:click="updateStatus" label="Update Status" black class="mt-4 w-full" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
