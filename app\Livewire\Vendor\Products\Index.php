<?php

namespace App\Livewire\Vendor\Products;

use App\Models\Product;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

#[Layout('layouts.vendor')]
class Index extends Component
{
    use WithPagination;

    public function deleteProduct($productId)
    {
        $product = Auth::user()->vendor->products()->findOrFail($productId);

        $product->delete();

        session()->flash('success', 'Product deleted successfully.');
    }

    public function render()
    {
        $products = Auth::user()->vendor->products()->with('category')->latest()->paginate(10);

        return view('livewire.vendor.products.index', [
            'products' => $products,
        ]);
    }
}
