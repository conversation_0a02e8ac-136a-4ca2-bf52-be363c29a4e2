<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('shipping_lga')->nullable()->after('shipping_city');
            $table->decimal('shipping_cost', 10, 2)->default(0.00)->after('shipping_method');
            $table->string('shipbubble_token')->nullable()->after('shipping_cost');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['shipping_lga', 'shipping_cost', 'shipbubble_token']);
        });
    }
};
