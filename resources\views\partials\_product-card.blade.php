<div class="group relative border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow duration-300">
    <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200">
        <a href="{{ route('products.show', $product->slug) }}">
            <img src="{{ $product->image_url ?? asset('storage/product-placeholder.jpg') }}" alt="{{ $product->name }}" class="h-full w-full object-cover object-center group-hover:opacity-75 transition-opacity duration-300">
        </a>
        @if ($product->discounted_price)
            <div class="absolute top-3 left-3 bg-black text-white text-xs font-bold px-2 py-1 rounded">
                -{{ $product->getDiscountPercentage() }}%
            </div>
        @endif
        <div class="absolute top-3 right-3">
            {{-- This assumes a parent Livewire component handles the wishlist toggle --}}
            <button wire:click.prevent="toggleWishlist({{ $product->id }})" class="text-black bg-white rounded-full p-2 hover:bg-gray-100 transition">
                <i class="{{ $product->isWishlisted() ? 'fa-solid' : 'fa-regular' }} fa-heart"></i>
            </button>
        </div>
    </div>
    <div class="p-4 bg-white">
        <h3 class="text-sm text-gray-500">
            <a href="{{ route('products.show', $product->slug) }}">
                {{ $product->category->name ?? 'Uncategorized' }}
            </a>
        </h3>
        <p class="text-base font-semibold text-gray-900 mt-1">
            <a href="{{ route('products.show', $product->slug) }}">
                {{ \Illuminate\Support\Str::limit($product->name, 40) }}
            </a>
        </p>
        <div class="flex items-baseline justify-between mt-2">
            @if ($product->discounted_price)
                <p class="text-lg font-bold text-black">
                    ₦{{ number_format($product->discounted_price, 2) }}
                    <span class="text-sm font-normal text-gray-500 line-through ml-2">₦{{ number_format($product->price, 2) }}</span>
                </p>
            @else
                <p class="text-lg font-bold text-black">₦{{ number_format($product->price, 2) }}</p>
            @endif
        </div>
        @livewire('add-to-cart-button', ['product' => $product], key($product->id))
    </div>
</div>
